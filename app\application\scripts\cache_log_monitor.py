#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de Monitoreo de Logs del Sistema de Caché
================================================

Este script analiza los logs del sistema de caché de Kaufman Pro,
detecta patrones anómalos, errores y problemas de rendimiento.
Genera alertas automáticas por correo electrónico cuando se detectan problemas críticos.

Requisitos:
- Python 3.8+
- Librerías: re, json, smtplib, email, datetime, os, sys, configparser, logging

Autor: Sistema de Monitoreo Kaufman Pro
Versión: 1.0.0
Fecha: 2025-07-17
"""

import os
import sys
import re
import json
import smtplib
import logging
import configparser
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, List, Tuple, Optional
from pathlib import Path

class CacheLogMonitor:
    """
    Monitor de logs del sistema de caché con detección de anomalías
    y sistema de alertas automáticas.
    """
    
    def __init__(self, config_file: str = None):
        """
        Inicializa el monitor de logs.

        Args:
            config_file (str): Ruta al archivo de configuración
        """
        self.config_file = config_file or self._get_default_config_path()
        self.logger = self._setup_logging()
        self.config = self._load_config()
        
        # Patrones de detección - SISTEMA INTEGRAL DE MONITOREO
        self.error_patterns = [
            # === ERRORES DE CACHÉ (ORIGINALES) ===
            r'ERROR.*cache.*failed',
            r'ERROR.*redis.*connection',
            r'ERROR.*predis.*error',
            r'CRITICAL.*cache.*timeout',
            r'ERROR.*Cache.*isolation.*failed',
            r'ERROR.*User.*context.*detection.*failed',

            # === ERRORES CRÍTICOS DE BASE DE DATOS ===
            r'ERROR.*database.*connection.*failed',
            r'ERROR.*mysql.*server.*has.*gone.*away',
            r'ERROR.*connection.*timed.*out',
            r'ERROR.*deadlock.*found',
            r'ERROR.*lock.*wait.*timeout',
            r'ERROR.*table.*doesn.*t.*exist',
            r'ERROR.*access.*denied.*for.*user',
            r'ERROR.*too.*many.*connections',
            r'ERROR.*disk.*full',
            r'CRITICAL.*database.*error',
            r'ERROR.*sql.*syntax.*error',
            r'ERROR.*duplicate.*entry',
            r'ERROR.*foreign.*key.*constraint',

            # === ERRORES DE MEMORIA ===
            r'FATAL.*out.*of.*memory',
            r'ERROR.*memory.*limit.*exceeded',
            r'CRITICAL.*memory.*allocation.*failed',
            r'ERROR.*maximum.*execution.*time.*exceeded',
            r'FATAL.*allowed.*memory.*size.*exhausted',
            r'ERROR.*memory.*leak.*detected',

            # === ERRORES DE SISTEMA DE ARCHIVOS ===
            r'ERROR.*permission.*denied',
            r'ERROR.*no.*such.*file.*or.*directory',
            r'ERROR.*disk.*space.*full',
            r'ERROR.*file.*not.*found',
            r'ERROR.*cannot.*write.*to.*file',
            r'ERROR.*failed.*to.*open.*stream',
            r'CRITICAL.*filesystem.*error',
            r'ERROR.*directory.*not.*writable',

            # === ERRORES DE RED Y CONECTIVIDAD ===
            r'ERROR.*connection.*refused',
            r'ERROR.*network.*unreachable',
            r'ERROR.*timeout.*occurred',
            r'ERROR.*ssl.*connection.*error',
            r'ERROR.*curl.*error',
            r'CRITICAL.*network.*failure',
            r'ERROR.*socket.*error',
            r'ERROR.*dns.*resolution.*failed',

            # === ERRORES DE AUTENTICACIÓN Y SESIONES ===
            r'ERROR.*authentication.*failed',
            r'ERROR.*session.*expired',
            r'ERROR.*invalid.*credentials',
            r'ERROR.*access.*token.*invalid',
            r'CRITICAL.*security.*breach',
            r'ERROR.*unauthorized.*access',
            r'ERROR.*login.*failed',
            r'ERROR.*session.*hijack.*detected',

            # === ERRORES FATALES DE PHP ===
            r'FATAL.*error',
            r'PARSE.*error',
            r'ERROR.*class.*not.*found',
            r'ERROR.*function.*not.*found',
            r'ERROR.*undefined.*method',
            r'ERROR.*undefined.*variable',
            r'ERROR.*call.*to.*undefined',
            r'FATAL.*cannot.*redeclare',
            r'ERROR.*syntax.*error.*unexpected',

            # === ERRORES DE APLICACIÓN QUE CAUSAN CRASHES ===
            r'ERROR.*application.*crash',
            r'CRITICAL.*system.*failure',
            r'ERROR.*segmentation.*fault',
            r'ERROR.*stack.*overflow',
            r'ERROR.*infinite.*loop.*detected',
            r'CRITICAL.*service.*unavailable',
            r'ERROR.*process.*killed',
            r'ERROR.*server.*error.*500',
            r'CRITICAL.*exception.*not.*caught',

            # === ERRORES DE CONFIGURACIÓN CRÍTICOS ===
            r'ERROR.*configuration.*error',
            r'ERROR.*missing.*required.*parameter',
            r'ERROR.*invalid.*configuration',
            r'CRITICAL.*config.*file.*not.*found',
            r'ERROR.*environment.*variable.*not.*set',

            # === ERRORES DE RECURSOS DEL SISTEMA ===
            r'ERROR.*resource.*temporarily.*unavailable',
            r'ERROR.*too.*many.*open.*files',
            r'CRITICAL.*system.*overload',
            r'ERROR.*cpu.*usage.*critical',
            r'ERROR.*load.*average.*too.*high'
        ]
        
        self.performance_patterns = [
            # === PROBLEMAS DE RENDIMIENTO DE CACHÉ (ORIGINALES) ===
            r'Cache.*slow.*query.*(\d+\.?\d*).*seconds',
            r'Cache.*operation.*took.*(\d+\.?\d*).*ms',
            r'Redis.*connection.*timeout',
            r'Cache.*hit.*rate.*below.*threshold',

            # === PROBLEMAS DE RENDIMIENTO DE BASE DE DATOS ===
            r'slow.*query.*(\d+\.?\d*).*seconds',
            r'query.*took.*(\d+\.?\d*).*seconds',
            r'database.*response.*time.*high',
            r'mysql.*slow.*log',
            r'query.*execution.*time.*exceeded',
            r'database.*connection.*pool.*exhausted',

            # === PROBLEMAS DE RENDIMIENTO DEL SISTEMA ===
            r'high.*cpu.*usage.*detected',
            r'memory.*usage.*above.*threshold',
            r'disk.*io.*high',
            r'load.*average.*warning',
            r'response.*time.*degraded',
            r'request.*processing.*slow',
            r'page.*load.*time.*exceeded',

            # === PROBLEMAS DE RENDIMIENTO DE RED ===
            r'network.*latency.*high',
            r'bandwidth.*usage.*critical',
            r'connection.*pool.*exhausted',
            r'api.*response.*time.*slow',

            # === PROBLEMAS DE RENDIMIENTO DE APLICACIÓN ===
            r'script.*execution.*time.*limit',
            r'function.*call.*took.*(\d+\.?\d*).*seconds',
            r'loop.*iteration.*count.*high',
            r'recursive.*call.*depth.*exceeded'
        ]
        
        self.security_patterns = [
            # === PROBLEMAS DE SEGURIDAD DE CACHÉ (ORIGINALES) ===
            r'Cache.*isolation.*violation',
            r'Cross.*user.*cache.*access.*detected',
            r'Suspicious.*cache.*access.*pattern',
            r'Cache.*key.*collision.*detected',

            # === ATAQUES Y INTENTOS DE INTRUSIÓN ===
            r'sql.*injection.*attempt',
            r'xss.*attack.*detected',
            r'csrf.*token.*mismatch',
            r'brute.*force.*attack',
            r'multiple.*failed.*login.*attempts',
            r'suspicious.*login.*pattern',
            r'unauthorized.*access.*attempt',
            r'privilege.*escalation.*attempt',

            # === PROBLEMAS DE AUTENTICACIÓN Y AUTORIZACIÓN ===
            r'authentication.*bypass.*attempt',
            r'session.*fixation.*detected',
            r'session.*hijacking.*attempt',
            r'invalid.*session.*token',
            r'expired.*authentication.*token',
            r'unauthorized.*api.*access',

            # === VIOLACIONES DE SEGURIDAD DE DATOS ===
            r'data.*breach.*detected',
            r'sensitive.*data.*exposure',
            r'unauthorized.*data.*access',
            r'data.*integrity.*violation',
            r'encryption.*failure',
            r'decryption.*error',

            # === PROBLEMAS DE CONFIGURACIÓN DE SEGURIDAD ===
            r'insecure.*configuration.*detected',
            r'weak.*password.*policy',
            r'ssl.*certificate.*error',
            r'tls.*handshake.*failed',
            r'security.*header.*missing',

            # === ACTIVIDAD MALICIOSA ===
            r'malware.*detected',
            r'virus.*scan.*positive',
            r'suspicious.*file.*upload',
            r'code.*injection.*attempt',
            r'path.*traversal.*attempt',
            r'directory.*listing.*exposed',

            # === PROBLEMAS DE INTEGRIDAD DEL SISTEMA ===
            r'file.*integrity.*check.*failed',
            r'checksum.*mismatch',
            r'unauthorized.*file.*modification',
            r'system.*file.*corruption'
        ]

        # === NUEVAS CATEGORÍAS DE PATRONES CRÍTICOS ===

        # Patrones de errores fatales del sistema
        self.fatal_system_patterns = [
            r'FATAL.*system.*crash',
            r'CRITICAL.*kernel.*panic',
            r'FATAL.*segmentation.*fault',
            r'CRITICAL.*out.*of.*memory',
            r'FATAL.*stack.*overflow',
            r'CRITICAL.*disk.*full',
            r'FATAL.*cannot.*allocate.*memory',
            r'CRITICAL.*service.*down',
            r'FATAL.*database.*corruption',
            r'CRITICAL.*filesystem.*corruption'
        ]

        # Patrones de errores de aplicación críticos
        self.application_critical_patterns = [
            r'ERROR.*Call.*to.*undefined.*method',
            r'FATAL.*Class.*not.*found',
            r'ERROR.*Maximum.*execution.*time',
            r'FATAL.*Allowed.*memory.*size.*exhausted',
            r'ERROR.*Cannot.*redeclare.*function',
            r'FATAL.*require.*failed.*opening',
            r'ERROR.*Undefined.*variable',
            r'FATAL.*syntax.*error',
            r'ERROR.*Division.*by.*zero',
            r'FATAL.*Cannot.*use.*object'
        ]

        # Patrones de errores de conectividad críticos
        self.connectivity_critical_patterns = [
            r'ERROR.*Connection.*refused',
            r'ERROR.*Network.*is.*unreachable',
            r'ERROR.*Operation.*timed.*out',
            r'CRITICAL.*DNS.*resolution.*failed',
            r'ERROR.*SSL.*connection.*error',
            r'CRITICAL.*Certificate.*verification.*failed',
            r'ERROR.*Socket.*connection.*failed',
            r'CRITICAL.*API.*endpoint.*unreachable'
        ]

        # Contadores de alertas expandidos
        self.alert_counts = {
            'errors': 0,
            'performance': 0,
            'security': 0,
            'warnings': 0,
            'fatal_system': 0,
            'application_critical': 0,
            'connectivity_critical': 0,
            'database_critical': 0,
            'memory_critical': 0,
            'filesystem_critical': 0
        }
        
        # Almacén de eventos detectados
        self.detected_events = []
        
    def _get_default_config_path(self) -> str:
        """Obtiene la ruta por defecto del archivo de configuración."""
        script_dir = Path(__file__).parent
        return str(script_dir / 'cache_monitor_config.ini')
    
    def _load_config(self) -> configparser.ConfigParser:
        """Carga la configuración desde el archivo INI."""
        config = configparser.ConfigParser()
        
        # Configuración por defecto
        config.read_dict({
            'PATHS': {
                'log_directory': '../logs',
                'output_directory': './reports',
                'temp_directory': './temp'
            },
            'EMAIL': {
                'smtp_server': 'localhost',
                'smtp_port': '587',
                'smtp_user': '',
                'smtp_password': '',
                'from_email': '<EMAIL>',
                'admin_emails': '<EMAIL>',
                'alert_emails': '<EMAIL>'
            },
            'MONITORING': {
                'check_interval_minutes': '15',
                'max_log_age_days': '7',
                'error_threshold': '10',
                'performance_threshold_ms': '1000',
                'enable_email_alerts': 'true',
                'enable_reports': 'true'
            },
            'ALERTS': {
                'critical_error_threshold': '5',
                'performance_alert_threshold': '3',
                'security_alert_threshold': '1',
                'alert_cooldown_minutes': '60'
            }
        })
        
        # Cargar configuración personalizada si existe
        if os.path.exists(self.config_file):
            config.read(self.config_file)
            self.logger.info(f"Configuración cargada desde: {self.config_file}")
        else:
            self.logger.warning(f"Archivo de configuración no encontrado: {self.config_file}")
            self.logger.info("Usando configuración por defecto")
            
        return config
    
    def _setup_logging(self) -> logging.Logger:
        """Configura el sistema de logging del monitor."""
        logger = logging.getLogger('CacheLogMonitor')

        # Evitar duplicar handlers si ya están configurados
        if logger.handlers:
            return logger

        logger.setLevel(logging.INFO)

        # Crear directorio de logs si no existe
        log_dir = Path(__file__).parent / 'logs'
        log_dir.mkdir(exist_ok=True)

        # Handler para archivo
        file_handler = logging.FileHandler(
            log_dir / f'cache_monitor_{datetime.now().strftime("%Y%m%d")}.log'
        )
        file_handler.setLevel(logging.INFO)

        # Handler para consola
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # Formato
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger
    
    def get_log_files(self) -> List[Path]:
        """
        Obtiene la lista de archivos de log a analizar.
        
        Returns:
            List[Path]: Lista de rutas de archivos de log
        """
        log_dir = Path(__file__).parent / self.config.get('PATHS', 'log_directory')
        
        if not log_dir.exists():
            self.logger.error(f"Directorio de logs no encontrado: {log_dir}")
            return []
        
        # Obtener archivos de log recientes
        max_age = int(self.config.get('MONITORING', 'max_log_age_days'))
        cutoff_date = datetime.now() - timedelta(days=max_age)
        
        log_files = []
        for log_file in log_dir.glob('log-*.php'):
            try:
                # Extraer fecha del nombre del archivo
                date_str = log_file.stem.replace('log-', '')
                file_date = datetime.strptime(date_str, '%Y-%m-%d')
                
                if file_date >= cutoff_date:
                    log_files.append(log_file)
                    
            except ValueError:
                self.logger.warning(f"No se pudo parsear la fecha del archivo: {log_file}")
                continue
        
        log_files.sort(reverse=True)  # Más recientes primero
        self.logger.info(f"Encontrados {len(log_files)} archivos de log para analizar")
        
        return log_files
    
    def parse_log_file(self, log_file: Path) -> List[Dict]:
        """
        Parsea un archivo de log y extrae eventos relevantes.
        
        Args:
            log_file (Path): Ruta del archivo de log
            
        Returns:
            List[Dict]: Lista de eventos detectados
        """
        events = []
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Remover tags PHP del inicio y final
            content = re.sub(r'<\?php.*?\?>', '', content, flags=re.DOTALL)
            
            # Dividir en líneas
            lines = content.strip().split('\n')
            
            for line_num, line in enumerate(lines, 1):
                if not line.strip():
                    continue
                    
                # Parsear línea de log
                event = self._parse_log_line(line, log_file.name, line_num)
                if event:
                    events.append(event)
                    
        except Exception as e:
            self.logger.error(f"Error parseando archivo {log_file}: {e}")
            
        return events
    
    def _parse_log_line(self, line: str, filename: str, line_num: int) -> Optional[Dict]:
        """
        Parsea una línea individual del log.
        
        Args:
            line (str): Línea del log
            filename (str): Nombre del archivo
            line_num (int): Número de línea
            
        Returns:
            Optional[Dict]: Evento detectado o None
        """
        # Patrón típico de log de CodeIgniter
        log_pattern = r'(\w+)\s*-\s*(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s*-->\s*(.*)'
        match = re.match(log_pattern, line)
        
        if not match:
            return None
            
        level, timestamp, message = match.groups()
        
        # Crear evento base
        event = {
            'timestamp': timestamp,
            'level': level,
            'message': message,
            'filename': filename,
            'line_number': line_num,
            'categories': []
        }
        
        # === DETECCIÓN INTEGRAL DE ERRORES CRÍTICOS ===
        # Filtrar solo WARNING para evitar saturación
        if level == 'WARNING':
            return None

        # Detectar TODOS los tipos de errores críticos, no solo caché
        event_detected = False

        # 1. Errores fatales del sistema (PRIORIDAD MÁXIMA)
        if self._matches_patterns(message, self.fatal_system_patterns):
            event['categories'].extend(['fatal_system', 'critical'])
            self.alert_counts['fatal_system'] += 1
            event_detected = True

        # 2. Errores críticos de aplicación
        if self._matches_patterns(message, self.application_critical_patterns):
            event['categories'].extend(['application_critical', 'critical'])
            self.alert_counts['application_critical'] += 1
            event_detected = True

        # 3. Errores críticos de conectividad
        if self._matches_patterns(message, self.connectivity_critical_patterns):
            event['categories'].extend(['connectivity_critical', 'critical'])
            self.alert_counts['connectivity_critical'] += 1
            event_detected = True

        # 4. Errores generales del sistema
        if self._matches_patterns(message, self.error_patterns):
            event['categories'].append('error')
            self.alert_counts['errors'] += 1
            event_detected = True

            # Subcategorizar errores específicos
            if self._is_database_related(message):
                event['categories'].append('database_critical')
                self.alert_counts['database_critical'] += 1

            if self._is_memory_related(message):
                event['categories'].append('memory_critical')
                self.alert_counts['memory_critical'] += 1

            if self._is_filesystem_related(message):
                event['categories'].append('filesystem_critical')
                self.alert_counts['filesystem_critical'] += 1

        # 5. Problemas de rendimiento
        if self._matches_patterns(message, self.performance_patterns):
            event['categories'].append('performance')
            self.alert_counts['performance'] += 1
            event_detected = True

        # 6. Problemas de seguridad
        if self._matches_patterns(message, self.security_patterns):
            event['categories'].append('security')
            self.alert_counts['security'] += 1
            event_detected = True

        # 7. Identificar si está relacionado con caché (para compatibilidad)
        if self._is_cache_related(message):
            event['categories'].append('cache')
            event_detected = True

        # Solo retornar eventos que coincidan con patrones críticos
        return event if event_detected else None
    
    def _is_cache_related(self, message: str) -> bool:
        """Determina si un mensaje está relacionado con caché."""
        cache_keywords = [
            'cache', 'redis', 'predis', 'isolation', 'user_context',
            'Cache_service', 'Cache_abstraction', 'TTL', 'hit', 'miss'
        ]
        
        message_lower = message.lower()
        return any(keyword.lower() in message_lower for keyword in cache_keywords)
    
    def _matches_patterns(self, message: str, patterns: List[str]) -> bool:
        """Verifica si un mensaje coincide con algún patrón."""
        return any(re.search(pattern, message, re.IGNORECASE) for pattern in patterns)

    def _is_database_related(self, message: str) -> bool:
        """Determina si un mensaje está relacionado con base de datos."""
        db_keywords = [
            'database', 'mysql', 'sql', 'query', 'table', 'connection',
            'deadlock', 'timeout', 'duplicate', 'foreign key', 'constraint'
        ]
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in db_keywords)

    def _is_memory_related(self, message: str) -> bool:
        """Determina si un mensaje está relacionado con memoria."""
        memory_keywords = [
            'memory', 'out of memory', 'memory limit', 'memory exhausted',
            'allocation failed', 'memory leak', 'stack overflow'
        ]
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in memory_keywords)

    def _is_filesystem_related(self, message: str) -> bool:
        """Determina si un mensaje está relacionado con sistema de archivos."""
        fs_keywords = [
            'file', 'directory', 'permission', 'disk', 'filesystem',
            'no such file', 'cannot write', 'disk full', 'not writable'
        ]
        message_lower = message.lower()
        return any(keyword in message_lower for keyword in fs_keywords)

    def analyze_events(self, events: List[Dict]) -> Dict:
        """
        Analiza los eventos detectados y genera estadísticas.

        Args:
            events (List[Dict]): Lista de eventos

        Returns:
            Dict: Análisis de eventos
        """
        analysis = {
            'total_events': len(events),
            'by_level': {},
            'by_category': {},
            'timeline': {},
            'anomalies': [],
            'recommendations': []
        }

        # Análisis por nivel
        for event in events:
            level = event['level']
            analysis['by_level'][level] = analysis['by_level'].get(level, 0) + 1

            # Análisis por categoría
            for category in event['categories']:
                analysis['by_category'][category] = analysis['by_category'].get(category, 0) + 1

            # Timeline (por hora)
            try:
                dt = datetime.strptime(event['timestamp'], '%Y-%m-%d %H:%M:%S')
                hour_key = dt.strftime('%Y-%m-%d %H:00')
                analysis['timeline'][hour_key] = analysis['timeline'].get(hour_key, 0) + 1
            except ValueError:
                continue

        # Detectar anomalías
        analysis['anomalies'] = self._detect_anomalies(events)

        # Generar recomendaciones
        analysis['recommendations'] = self._generate_recommendations(analysis)

        return analysis

    def _detect_anomalies(self, events: List[Dict]) -> List[Dict]:
        """Detecta patrones anómalos en los eventos - SISTEMA INTEGRAL."""
        anomalies = []

        # === DETECCIÓN DE ERRORES FATALES DEL SISTEMA (PRIORIDAD MÁXIMA) ===
        fatal_events = [e for e in events if 'fatal_system' in e['categories']]
        if len(fatal_events) > 0:
            anomalies.append({
                'type': 'fatal_system_error',
                'severity': 'critical',
                'description': f'🚨 ERRORES FATALES DEL SISTEMA: {len(fatal_events)} eventos críticos detectados',
                'count': len(fatal_events),
                'priority': 1
            })

        # === DETECCIÓN DE ERRORES CRÍTICOS DE APLICACIÓN ===
        app_critical_events = [e for e in events if 'application_critical' in e['categories']]
        if len(app_critical_events) > 0:
            anomalies.append({
                'type': 'application_critical_error',
                'severity': 'critical',
                'description': f'💥 ERRORES CRÍTICOS DE APLICACIÓN: {len(app_critical_events)} fallos detectados',
                'count': len(app_critical_events),
                'priority': 2
            })

        # === DETECCIÓN DE ERRORES DE CONECTIVIDAD CRÍTICOS ===
        connectivity_events = [e for e in events if 'connectivity_critical' in e['categories']]
        if len(connectivity_events) > 2:  # Más de 2 errores de conectividad es crítico
            anomalies.append({
                'type': 'connectivity_critical_error',
                'severity': 'critical',
                'description': f'🌐 ERRORES CRÍTICOS DE CONECTIVIDAD: {len(connectivity_events)} fallos de conexión',
                'count': len(connectivity_events),
                'priority': 3
            })

        # === DETECCIÓN DE ERRORES DE BASE DE DATOS ===
        db_events = [e for e in events if 'database_critical' in e['categories']]
        if len(db_events) > 3:
            anomalies.append({
                'type': 'database_critical_error',
                'severity': 'high',
                'description': f'🗄️ ERRORES CRÍTICOS DE BASE DE DATOS: {len(db_events)} problemas detectados',
                'count': len(db_events),
                'priority': 4
            })

        # === DETECCIÓN DE ERRORES DE MEMORIA ===
        memory_events = [e for e in events if 'memory_critical' in e['categories']]
        if len(memory_events) > 1:  # Cualquier error de memoria es crítico
            anomalies.append({
                'type': 'memory_critical_error',
                'severity': 'critical',
                'description': f'🧠 ERRORES CRÍTICOS DE MEMORIA: {len(memory_events)} problemas de memoria',
                'count': len(memory_events),
                'priority': 5
            })

        # === DETECCIÓN DE ERRORES DE SISTEMA DE ARCHIVOS ===
        fs_events = [e for e in events if 'filesystem_critical' in e['categories']]
        if len(fs_events) > 2:
            anomalies.append({
                'type': 'filesystem_critical_error',
                'severity': 'high',
                'description': f'📁 ERRORES CRÍTICOS DE SISTEMA DE ARCHIVOS: {len(fs_events)} problemas detectados',
                'count': len(fs_events),
                'priority': 6
            })

        # === DETECCIÓN DE PICOS DE ERRORES GENERALES ===
        error_events = [e for e in events if 'error' in e['categories']]
        error_threshold = int(self.config.get('MONITORING', 'error_threshold'))
        if len(error_events) > error_threshold:
            anomalies.append({
                'type': 'error_spike',
                'severity': 'high',
                'description': f'📈 PICO DE ERRORES GENERALES: {len(error_events)} errores (umbral: {error_threshold})',
                'count': len(error_events),
                'priority': 7
            })

        # === DETECCIÓN DE PROBLEMAS DE RENDIMIENTO ===
        perf_events = [e for e in events if 'performance' in e['categories']]
        perf_threshold = int(self.config.get('ALERTS', 'performance_alert_threshold'))
        if len(perf_events) > perf_threshold:
            anomalies.append({
                'type': 'performance_degradation',
                'severity': 'medium',
                'description': f'⚡ DEGRADACIÓN DE RENDIMIENTO: {len(perf_events)} eventos (umbral: {perf_threshold})',
                'count': len(perf_events),
                'priority': 8
            })

        # === DETECCIÓN DE PROBLEMAS DE SEGURIDAD ===
        security_events = [e for e in events if 'security' in e['categories']]
        if len(security_events) > 0:
            anomalies.append({
                'type': 'security_issue',
                'severity': 'critical',
                'description': f'🔒 PROBLEMAS DE SEGURIDAD: {len(security_events)} incidentes de seguridad',
                'count': len(security_events),
                'priority': 9
            })

        # Ordenar anomalías por prioridad
        anomalies.sort(key=lambda x: x.get('priority', 999))

        return anomalies

    def _generate_recommendations(self, analysis: Dict) -> List[str]:
        """Genera recomendaciones basadas en el análisis - SISTEMA INTEGRAL."""
        recommendations = []

        # === RECOMENDACIONES PARA ERRORES FATALES DEL SISTEMA ===
        fatal_count = analysis['by_category'].get('fatal_system', 0)
        if fatal_count > 0:
            recommendations.append(
                f"🚨 ACCIÓN INMEDIATA REQUERIDA: {fatal_count} errores fatales del sistema detectados. "
                "Revisar logs del sistema, reiniciar servicios críticos y verificar integridad del hardware."
            )

        # === RECOMENDACIONES PARA ERRORES CRÍTICOS DE APLICACIÓN ===
        app_critical_count = analysis['by_category'].get('application_critical', 0)
        if app_critical_count > 0:
            recommendations.append(
                f"💥 ERRORES CRÍTICOS DE APLICACIÓN ({app_critical_count}): "
                "Revisar código PHP, verificar dependencias, comprobar sintaxis y configuración de autoload."
            )

        # === RECOMENDACIONES PARA ERRORES DE CONECTIVIDAD ===
        connectivity_count = analysis['by_category'].get('connectivity_critical', 0)
        if connectivity_count > 0:
            recommendations.append(
                f"🌐 PROBLEMAS DE CONECTIVIDAD ({connectivity_count}): "
                "Verificar conexiones de red, DNS, certificados SSL y disponibilidad de servicios externos."
            )

        # === RECOMENDACIONES PARA ERRORES DE BASE DE DATOS ===
        db_count = analysis['by_category'].get('database_critical', 0)
        if db_count > 0:
            recommendations.append(
                f"🗄️ ERRORES DE BASE DE DATOS ({db_count}): "
                "Revisar conexiones MySQL, optimizar consultas, verificar espacio en disco y configuración de timeouts."
            )

        # === RECOMENDACIONES PARA ERRORES DE MEMORIA ===
        memory_count = analysis['by_category'].get('memory_critical', 0)
        if memory_count > 0:
            recommendations.append(
                f"🧠 PROBLEMAS DE MEMORIA ({memory_count}): "
                "Aumentar memory_limit de PHP, optimizar consultas que consumen memoria, revisar loops infinitos."
            )

        # === RECOMENDACIONES PARA ERRORES DE SISTEMA DE ARCHIVOS ===
        fs_count = analysis['by_category'].get('filesystem_critical', 0)
        if fs_count > 0:
            recommendations.append(
                f"📁 ERRORES DE SISTEMA DE ARCHIVOS ({fs_count}): "
                "Verificar permisos de archivos, espacio en disco, integridad del filesystem y configuración de paths."
            )

        # === RECOMENDACIONES PARA ERRORES GENERALES ===
        error_count = analysis['by_category'].get('error', 0)
        if error_count > 10:
            recommendations.append(
                f"📈 ALTO VOLUMEN DE ERRORES ({error_count}): "
                "Revisar logs detallados, identificar patrones comunes y implementar fixes preventivos."
            )
        elif error_count > 5:
            recommendations.append(
                f"⚠️ ERRORES MODERADOS ({error_count}): "
                "Monitorear tendencias y considerar optimizaciones preventivas."
            )

        # === RECOMENDACIONES PARA PROBLEMAS DE RENDIMIENTO ===
        perf_count = analysis['by_category'].get('performance', 0)
        if perf_count > 5:
            recommendations.append(
                f"⚡ DEGRADACIÓN DE RENDIMIENTO ({perf_count}): "
                "Optimizar consultas lentas, ajustar configuración de caché, revisar índices de base de datos."
            )
        elif perf_count > 3:
            recommendations.append(
                f"🐌 PROBLEMAS DE RENDIMIENTO ({perf_count}): "
                "Considerar optimización de consultas y ajuste de TTL de caché."
            )

        # === RECOMENDACIONES PARA PROBLEMAS DE SEGURIDAD ===
        security_count = analysis['by_category'].get('security', 0)
        if security_count > 0:
            recommendations.append(
                f"🔒 INCIDENTES DE SEGURIDAD ({security_count}): "
                "REVISAR INMEDIATAMENTE - Verificar logs de acceso, cambiar credenciales comprometidas, "
                "revisar aislamiento de usuarios y implementar medidas de seguridad adicionales."
            )

        # === RECOMENDACIONES PARA CACHÉ (COMPATIBILIDAD) ===
        cache_count = analysis['by_category'].get('cache', 0)
        if cache_count > 0 and error_count == 0:
            recommendations.append(
                f"✅ SISTEMA DE CACHÉ ESTABLE ({cache_count} eventos): "
                "Continuar monitoreo regular y mantener configuración actual."
            )

        # === RECOMENDACIÓN GENERAL SI NO HAY PROBLEMAS ===
        if not recommendations:
            recommendations.append(
                "✅ SISTEMA FUNCIONANDO ÓPTIMAMENTE: "
                "No se detectaron problemas críticos. Continuar monitoreo preventivo."
            )

        return recommendations

    def send_alert_email(self, analysis: Dict, events: List[Dict]) -> bool:
        """
        Envía alerta por correo electrónico.

        Args:
            analysis (Dict): Análisis de eventos
            events (List[Dict]): Eventos detectados

        Returns:
            bool: True si se envió correctamente
        """
        if not self.config.getboolean('MONITORING', 'enable_email_alerts'):
            self.logger.info("Alertas por email deshabilitadas")
            return False

        # Verificar si hay anomalías críticas
        critical_anomalies = [a for a in analysis['anomalies'] if a['severity'] == 'critical']
        if not critical_anomalies and analysis['by_category'].get('error', 0) < 5:
            self.logger.info("No hay anomalías críticas para alertar")
            return False

        try:
            # Configurar SMTP
            smtp_server = self.config.get('EMAIL', 'smtp_server')
            smtp_port = int(self.config.get('EMAIL', 'smtp_port'))
            smtp_user = self.config.get('EMAIL', 'smtp_user')
            smtp_password = self.config.get('EMAIL', 'smtp_password')

            # Crear mensaje
            msg = MIMEMultipart()
            msg['From'] = self.config.get('EMAIL', 'from_email')
            msg['To'] = self.config.get('EMAIL', 'alert_emails')
            msg['Subject'] = f"🚨 ALERTA: Problemas detectados en sistema de caché - {datetime.now().strftime('%Y-%m-%d %H:%M')}"

            # Cuerpo del mensaje
            body = self._create_alert_email_body(analysis, events)
            msg.attach(MIMEText(body, 'html'))

            # Enviar email
            if smtp_user and smtp_password:
                server = smtplib.SMTP(smtp_server, smtp_port)
                server.starttls()
                server.login(smtp_user, smtp_password)
            else:
                server = smtplib.SMTP(smtp_server, smtp_port)

            server.send_message(msg)
            server.quit()

            self.logger.info("Alerta enviada por email correctamente")
            return True

        except Exception as e:
            self.logger.error(f"Error enviando alerta por email: {e}")
            return False

    def _create_alert_email_body(self, analysis: Dict, events: List[Dict]) -> str:
        """Crea el cuerpo del email de alerta."""
        body = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #d9534f; color: white; padding: 15px; border-radius: 5px; }}
                .summary {{ background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                .anomaly {{ background-color: #fff3cd; padding: 10px; margin: 5px 0; border-left: 4px solid #ffc107; }}
                .critical {{ border-left-color: #dc3545; background-color: #f8d7da; }}
                .recommendation {{ background-color: #d1ecf1; padding: 10px; margin: 5px 0; border-left: 4px solid #17a2b8; }}
                table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>🚨 Alerta del Sistema de Monitoreo de Caché</h2>
                <p>Fecha y hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>

            <div class="summary">
                <h3>📊 Resumen de Eventos</h3>
                <ul>
                    <li><strong>Total de eventos:</strong> {analysis['total_events']}</li>
                    <li><strong>Errores:</strong> {analysis['by_category'].get('error', 0)}</li>
                    <li><strong>Problemas de rendimiento:</strong> {analysis['by_category'].get('performance', 0)}</li>
                    <li><strong>Problemas de seguridad:</strong> {analysis['by_category'].get('security', 0)}</li>
                </ul>
            </div>
        """

        # Agregar anomalías
        if analysis['anomalies']:
            body += "<h3>⚠️ Anomalías Detectadas</h3>"
            for anomaly in analysis['anomalies']:
                css_class = 'critical' if anomaly['severity'] == 'critical' else 'anomaly'
                body += f'<div class="{css_class}"><strong>{anomaly["type"].replace("_", " ").title()}:</strong> {anomaly["description"]}</div>'

        # Agregar recomendaciones
        if analysis['recommendations']:
            body += "<h3>💡 Recomendaciones</h3>"
            for rec in analysis['recommendations']:
                body += f'<div class="recommendation">{rec}</div>'

        body += """
            <p><em>Este es un mensaje automático del sistema de monitoreo de caché de Kaufman Pro.</em></p>
        </body>
        </html>
        """

        return body

    def generate_report(self, analysis: Dict, events: List[Dict]) -> str:
        """
        Genera un reporte detallado en formato JSON.

        Args:
            analysis (Dict): Análisis de eventos
            events (List[Dict]): Eventos detectados

        Returns:
            str: Ruta del archivo de reporte generado
        """
        if not self.config.getboolean('MONITORING', 'enable_reports'):
            self.logger.info("Generación de reportes deshabilitada")
            return ""

        try:
            # Crear directorio de reportes
            report_dir = Path(__file__).parent / self.config.get('PATHS', 'output_directory')
            report_dir.mkdir(exist_ok=True)

            # Crear reporte
            report = {
                'metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'monitor_version': '1.0.0',
                    'config_file': self.config_file
                },
                'summary': {
                    'total_events': analysis['total_events'],
                    'alert_counts': self.alert_counts,
                    'analysis_period': self._get_analysis_period(events)
                },
                'analysis': analysis,
                'events': events[:100],  # Limitar a 100 eventos más recientes
                'system_health': self._assess_system_health(analysis)
            }

            # Guardar reporte
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = report_dir / f'cache_monitor_report_{timestamp}.json'

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Reporte generado: {report_file}")
            return str(report_file)

        except Exception as e:
            self.logger.error(f"Error generando reporte: {e}")
            return ""

    def _get_analysis_period(self, events: List[Dict]) -> Dict:
        """Obtiene el período de análisis de los eventos."""
        if not events:
            return {}

        timestamps = []
        for event in events:
            try:
                dt = datetime.strptime(event['timestamp'], '%Y-%m-%d %H:%M:%S')
                timestamps.append(dt)
            except ValueError:
                continue

        if not timestamps:
            return {}

        return {
            'start': min(timestamps).isoformat(),
            'end': max(timestamps).isoformat(),
            'duration_hours': (max(timestamps) - min(timestamps)).total_seconds() / 3600
        }

    def _assess_system_health(self, analysis: Dict) -> Dict:
        """Evalúa la salud general del sistema."""
        error_count = analysis['by_category'].get('error', 0)
        perf_count = analysis['by_category'].get('performance', 0)
        security_count = analysis['by_category'].get('security', 0)

        # Calcular score de salud (0-100)
        health_score = 100
        health_score -= min(error_count * 5, 50)      # Errores penalizan hasta 50 puntos
        health_score -= min(perf_count * 3, 30)       # Rendimiento penaliza hasta 30 puntos
        health_score -= min(security_count * 20, 100) # Seguridad penaliza hasta 100 puntos

        health_score = max(0, health_score)

        # Determinar estado
        if health_score >= 90:
            status = 'excellent'
            status_text = 'Excelente'
        elif health_score >= 70:
            status = 'good'
            status_text = 'Bueno'
        elif health_score >= 50:
            status = 'warning'
            status_text = 'Advertencia'
        else:
            status = 'critical'
            status_text = 'Crítico'

        return {
            'score': health_score,
            'status': status,
            'status_text': status_text,
            'issues': {
                'errors': error_count,
                'performance': perf_count,
                'security': security_count
            }
        }

    def run_monitoring_cycle(self) -> Dict:
        """
        Ejecuta un ciclo completo de monitoreo.

        Returns:
            Dict: Resultados del monitoreo
        """
        self.logger.info("=== Iniciando ciclo de monitoreo de caché ===")

        try:
            # Obtener archivos de log
            log_files = self.get_log_files()
            if not log_files:
                self.logger.warning("No se encontraron archivos de log para analizar")
                return {'status': 'no_logs', 'message': 'No hay archivos de log disponibles'}

            # Procesar archivos de log
            all_events = []
            for log_file in log_files:
                self.logger.info(f"Procesando: {log_file}")
                events = self.parse_log_file(log_file)
                all_events.extend(events)
                self.logger.info(f"Encontrados {len(events)} eventos relacionados con caché")

            if not all_events:
                self.logger.info("No se encontraron eventos relacionados con caché")
                return {'status': 'no_cache_events', 'message': 'No hay eventos de caché para analizar'}

            # Analizar eventos
            self.logger.info(f"Analizando {len(all_events)} eventos de caché")
            analysis = self.analyze_events(all_events)

            # Generar reporte
            report_file = self.generate_report(analysis, all_events)

            # Enviar alertas si es necesario
            alert_sent = self.send_alert_email(analysis, all_events)

            # Resultado del monitoreo
            result = {
                'status': 'success',
                'timestamp': datetime.now().isoformat(),
                'events_processed': len(all_events),
                'analysis': analysis,
                'report_file': report_file,
                'alert_sent': alert_sent,
                'system_health': self._assess_system_health(analysis)
            }

            self.logger.info("=== Ciclo de monitoreo completado exitosamente ===")
            return result

        except Exception as e:
            self.logger.error(f"Error en ciclo de monitoreo: {e}")
            return {'status': 'error', 'message': str(e)}


def main():
    """Función principal del script."""
    import argparse

    parser = argparse.ArgumentParser(description='Monitor de logs del sistema de caché')
    parser.add_argument('--config', '-c', help='Archivo de configuración personalizado')
    parser.add_argument('--verbose', '-v', action='store_true', help='Modo verbose')
    parser.add_argument('--test', '-t', action='store_true', help='Modo de prueba (no envía emails)')

    args = parser.parse_args()

    # Crear monitor
    monitor = CacheLogMonitor(config_file=args.config)

    if args.verbose:
        monitor.logger.setLevel(logging.DEBUG)

    if args.test:
        monitor.config.set('MONITORING', 'enable_email_alerts', 'false')
        monitor.logger.info("Modo de prueba activado - No se enviarán emails")

    # Ejecutar monitoreo
    result = monitor.run_monitoring_cycle()

    # Mostrar resultado
    print(f"\n{'='*60}")
    print(f"RESULTADO DEL MONITOREO DE CACHÉ")
    print(f"{'='*60}")
    print(f"Estado: {result['status']}")

    if result['status'] == 'success':
        health = result['system_health']
        print(f"Salud del sistema: {health['status_text']} ({health['score']}/100)")
        print(f"Eventos procesados: {result['events_processed']}")
        print(f"Alerta enviada: {'Sí' if result['alert_sent'] else 'No'}")
        if result['report_file']:
            print(f"Reporte generado: {result['report_file']}")
    else:
        print(f"Mensaje: {result.get('message', 'Error desconocido')}")

    print(f"{'='*60}\n")


if __name__ == '__main__':
    main()
