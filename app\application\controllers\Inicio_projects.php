<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Inicio_projects extends MY_Controller {
    private $id_client_context_module;
    private $id_client_context_submodule;
    private $cache_duration = 3600; // 1 hora de caché

    public function __construct() {
        parent::__construct();
        $this->init_permission_checker("client");
        $this->load->library('Cache_service');

        $this->id_client_context_module = 3;
        $this->id_client_context_submodule = 0;

        $this->init_services();
        $this->check_client_access();
    }

    private function init_services(): void {
        // Inicialización de servicios y modelos necesarios
        $this->load->model([
            'Clients_model',
            'Projects_model',
            'General_settings_model',
            'Project_rel_footprints_model',
            'Calculation_model',
            'Fields_model',
            'Unity_model',
            'Forms_model',
            'Characterization_factors_model',
            'Module_footprint_units_model',
            'Conversion_model',
            'Unit_processes_model',
            'Tipo_tratamiento_model',
            'Compromises_rca_model',
            'Compromises_compliance_evaluation_rca_model',
            'Permitting_model',
            'Permitting_procedure_evaluation_model'
        ]);
    }

    private function check_client_access(): void {
        $id_cliente = $this->login_user->client_id;
        $this->block_url_client_context($id_cliente, $this->id_client_context_module);
    }

    public function index() {
        if ($this->login_user->user_type === "staff") {
            redirect('dashboard');
            return;
        }

        try {
            $this->reset_session_data();
            $view_data = $this->prepare_view_data();

            if (!$view_data['client_info']->habilitado) {
                $this->handle_disabled_client();
                return;
            }
            $this->render_view(view_data: $view_data);

        } catch (Exception $e) {
            log_message('error', 'Error en Inicio_projects/index: ' . $e->getMessage());
            show_error('Ha ocurrido un error al cargar la página', 500);
        }
    }

    private function reset_session_data(): void {
        $session_data = [
            'menu_project_active' => TRUE,
            'client_area' => NULL,
            'project_context' => NULL,
            'menu_agreements_active' => NULL,
            'menu_kpi_active' => NULL,
            'menu_help_and_support_active' => NULL,
            'menu_recordbook_active' => NULL,
            'menu_ec_active' => NULL,
            'menu_consolidated_impacts_active' => NULL
        ];

        foreach ($session_data as $key => $value) {
            $this->session->set_userdata($key, $value);
        }
    }

    private function prepare_view_data(): array {
        $client_info = $this->get_cached_client_info();
        $this->update_session_client_data($client_info);

        return [
            'client_info' => $client_info,
            'client_id' => $client_info->id,
            'projects' => $this->get_cached_projects($client_info->id),
            'page_type' => "dashboard",
            'perfil_puede_ver_compromisos' => $this->profile_access($this->session->user_id, 6, "ver", 3),
            'perfil_puede_ver_permisos' => $this->profile_access($this->session->user_id, 7, "ver", 5)
        ];
    }

    private function get_cached_client_info() {
        $cache_key = "client_info_{$this->login_user->client_id}";

        return $this->cache_service->remember(
            $cache_key,
            function() {
                $options = ["id" => $this->login_user->client_id];
                return $this->Clients_model->get_details($options)->row();
            },
            $this->cache_duration
        );
    }

    private function get_cached_projects(int $client_id) {
        $cache_key = "projects_member_{$this->login_user->id}_{$client_id}";

        return $this->cache_service->remember(
            $cache_key,
            function() use ($client_id) {
                return $this->Projects_model->get_projects_of_member(
                    $this->login_user->id,
                    $client_id
                )->result();
            },
            $this->cache_duration
        );
    }

    private function update_session_client_data($client_info): void {
        $this->session->set_userdata([
            'logo' => $client_info->logo,
            'bar_color' => $client_info->color_sitio
        ]);
    }

    private function handle_disabled_client(): void {
        $this->session->sess_destroy();
        redirect('signin/index/disabled');
    }

    private function render_view(array $view_data): void {
        try {
            $this->output->enable_profiler(FALSE);
            echo $this->template->rander("dashboard/inicio_projects", $view_data);
        } catch (Exception $e) {
            log_message('error', 'Error al renderizar la vista: ' . $e->getMessage());
            show_error('Error al cargar la página', 500);
        }
    }

    public function load_project_context(int $project_id = 0): void {
        try {
            $this->session->set_userdata('project_context', $project_id);
            redirect('dashboard');
        } catch (Exception $e) {
            log_message('error', 'Error al cargar el contexto del proyecto: ' . $e->getMessage());
            redirect('dashboard');
        }
    }

    public function invalidate_cache(): void {
        try {
            $this->cache_service->flush_prefix('projects_');
            $this->cache_service->flush_prefix('client_info_');
            redirect('inicio_projects');
        } catch (Exception $e) {
            log_message('error', 'Error al invalidar el caché: ' . $e->getMessage());
            redirect('inicio_projects');
        }
    }
}