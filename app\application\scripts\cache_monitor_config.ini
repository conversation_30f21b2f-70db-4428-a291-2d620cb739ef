# Configuración del Monitor de Logs de Caché
# =============================================
# 
# Este archivo contiene la configuración para el script de monitoreo
# de logs del sistema de caché de Kaufman Pro.
#
# Versión: 1.0.0
# Fecha: 2025-07-17

[PATHS]
# Directorio donde se encuentran los logs de CodeIgniter
log_directory = ../logs

# Directorio donde se guardarán los reportes generados
output_directory = ./reports

# Directorio temporal para archivos de trabajo
temp_directory = ./temp

[EMAIL]
# Configuración del servidor SMTP
smtp_server = localhost
smtp_port = 587

# Credenciales SMTP (dejar vacío si no se requiere autenticación)
smtp_user = 
smtp_password = 

# Direcciones de correo
from_email = <EMAIL>
admin_emails = <EMAIL>,<EMAIL>
alert_emails = <EMAIL>,<EMAIL>

[MONITORING]
# Intervalo de verificación en minutos
check_interval_minutes = 15

# Edad máxima de logs a analizar (en días)
max_log_age_days = 7

# === UMBRALES PARA SISTEMA INTEGRAL DE MONITOREO ===

# Umbral de errores generales para generar alerta (aumentado para evitar saturación)
error_threshold = 15

# Umbral de rendimiento en milisegundos
performance_threshold_ms = 1000

# Umbrales específicos para errores críticos
fatal_system_threshold = 1          # Cualquier error fatal es crítico
application_critical_threshold = 2  # 2+ errores de aplicación
connectivity_critical_threshold = 3 # 3+ errores de conectividad
database_critical_threshold = 5     # 5+ errores de base de datos
memory_critical_threshold = 2       # 2+ errores de memoria
filesystem_critical_threshold = 3   # 3+ errores de filesystem

# Habilitar alertas por email
enable_email_alerts = true

# Habilitar generación de reportes
enable_reports = true

# Nivel de logging: DEBUG, INFO, WARNING, ERROR
log_level = INFO

# Filtrar WARNING para evitar saturación (solo ERROR, CRITICAL, FATAL)
filter_warnings = true

[ALERTS]
# === UMBRALES DE ALERTAS PARA SISTEMA INTEGRAL ===

# Umbral de errores críticos para alerta inmediata (reducido para mayor sensibilidad)
critical_error_threshold = 3

# Umbral de alertas de rendimiento
performance_alert_threshold = 5

# Umbral de alertas de seguridad (cualquier evento de seguridad es crítico)
security_alert_threshold = 1

# Umbrales específicos para nuevas categorías
fatal_system_alert_threshold = 1      # Cualquier error fatal genera alerta inmediata
application_critical_alert_threshold = 2
connectivity_critical_alert_threshold = 3
database_critical_alert_threshold = 5
memory_critical_alert_threshold = 1   # Errores de memoria son muy críticos
filesystem_critical_alert_threshold = 3

# Tiempo de espera entre alertas del mismo tipo (en minutos)
alert_cooldown_minutes = 60

# Tipos de alertas habilitadas (expandido)
enable_error_alerts = true
enable_performance_alerts = true
enable_security_alerts = true
enable_fatal_system_alerts = true
enable_application_critical_alerts = true
enable_connectivity_critical_alerts = true
enable_database_critical_alerts = true
enable_memory_critical_alerts = true
enable_filesystem_critical_alerts = true

[PATTERNS]
# === PATRONES PERSONALIZADOS PARA SISTEMA INTEGRAL ===

# Patrones personalizados para errores críticos del sistema
custom_fatal_system_patterns =
    FATAL.*system.*crash.*detected
    CRITICAL.*kernel.*panic
    FATAL.*segmentation.*fault
    CRITICAL.*out.*of.*memory.*system
    FATAL.*disk.*corruption.*detected

# Patrones personalizados para errores críticos de aplicación
custom_application_critical_patterns =
    ERROR.*Call.*to.*undefined.*method.*Cache_service
    FATAL.*Class.*Users_model.*not.*found
    ERROR.*Maximum.*execution.*time.*of.*\d+.*seconds.*exceeded
    FATAL.*Allowed.*memory.*size.*of.*\d+.*bytes.*exhausted
    ERROR.*Cannot.*redeclare.*function

# Patrones personalizados para errores de base de datos
custom_database_critical_patterns =
    ERROR.*MySQL.*server.*has.*gone.*away
    ERROR.*Lost.*connection.*to.*MySQL.*server
    ERROR.*Deadlock.*found.*when.*trying.*to.*get.*lock
    ERROR.*Lock.*wait.*timeout.*exceeded
    ERROR.*Table.*doesn.*t.*exist

# Patrones personalizados para errores de memoria
custom_memory_critical_patterns =
    FATAL.*Out.*of.*memory.*\(allocated.*\d+\)
    ERROR.*Memory.*limit.*of.*\d+.*bytes.*exceeded
    CRITICAL.*Memory.*allocation.*failed
    ERROR.*Memory.*leak.*detected.*in.*process

# Patrones personalizados para errores de sistema de archivos
custom_filesystem_critical_patterns =
    ERROR.*Permission.*denied.*writing.*to
    ERROR.*No.*space.*left.*on.*device
    ERROR.*Disk.*quota.*exceeded
    ERROR.*File.*system.*is.*read-only

# Patrones personalizados para errores de conectividad
custom_connectivity_critical_patterns =
    ERROR.*Connection.*refused.*to.*redis
    ERROR.*Network.*is.*unreachable.*redis
    ERROR.*SSL.*connection.*error.*predis
    ERROR.*DNS.*resolution.*failed.*for.*redis

# Patrones personalizados para detección de errores (originales expandidos)
custom_error_patterns =
    ERROR.*cache.*connection.*lost
    CRITICAL.*redis.*memory.*full
    ERROR.*cache.*key.*not.*found
    ERROR.*predis.*connection.*timeout
    ERROR.*cache.*serialization.*failed

# Patrones personalizados para detección de problemas de rendimiento (expandidos)
custom_performance_patterns =
    Cache.*query.*exceeded.*timeout
    Slow.*cache.*operation.*detected
    Redis.*response.*time.*high
    Database.*query.*took.*\d+.*seconds
    Page.*load.*time.*exceeded.*\d+.*seconds

# Patrones personalizados para detección de problemas de seguridad (expandidos)
custom_security_patterns =
    Unauthorized.*cache.*access
    Cache.*data.*leak.*detected
    Invalid.*user.*context.*cache
    SQL.*injection.*attempt.*detected
    XSS.*attack.*attempt.*blocked
    Brute.*force.*login.*attempt

[REPORTS]
# Formato de reportes: json, html, csv
report_format = json

# Incluir eventos completos en el reporte
include_full_events = true

# Número máximo de eventos a incluir en reportes
max_events_in_report = 100

# Retener reportes por días
retain_reports_days = 30

# Generar reporte diario automático
generate_daily_report = true

# Hora para generar reporte diario (formato 24h)
daily_report_time = 08:00

[PERFORMANCE]
# Configuración de optimización del monitor

# Tamaño máximo de archivo de log a procesar (en MB)
max_log_file_size_mb = 50

# Número máximo de líneas a procesar por archivo
max_lines_per_file = 10000

# Timeout para operaciones de red (en segundos)
network_timeout_seconds = 30

# Usar procesamiento en paralelo para múltiples archivos
enable_parallel_processing = false

# Número de procesos paralelos (si está habilitado)
parallel_processes = 2

[NOTIFICATIONS]
# Configuración adicional de notificaciones

# Webhook URL para notificaciones (opcional)
webhook_url = 

# Slack webhook URL (opcional)
slack_webhook_url = 

# Teams webhook URL (opcional)
teams_webhook_url = 

# Incluir gráficos en notificaciones
include_charts = false

# Formato de timestamp en notificaciones
timestamp_format = %%Y-%%m-%%d %%H:%%M:%%S

[SECURITY]
# Configuración de seguridad del monitor

# Validar integridad de archivos de log
validate_log_integrity = true

# Cifrar reportes sensibles
encrypt_sensitive_reports = false

# Clave de cifrado (si está habilitado)
encryption_key = 

# Registrar acceso a archivos de configuración
log_config_access = true

# Modo de operación seguro (validaciones adicionales)
secure_mode = true

[ADVANCED]
# Configuraciones avanzadas

# Usar expresiones regulares compiladas para mejor rendimiento
use_compiled_regex = true

# Caché de patrones en memoria
cache_patterns = true

# Límite de memoria para el proceso (en MB)
memory_limit_mb = 512

# Timeout total para el ciclo de monitoreo (en minutos)
total_timeout_minutes = 30

# Reintentos en caso de error
max_retries = 3

# Intervalo entre reintentos (en segundos)
retry_interval_seconds = 5
