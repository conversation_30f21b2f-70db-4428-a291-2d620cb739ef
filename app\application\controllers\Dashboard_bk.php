<?php
require_once(APPPATH . 'services/FootprintService.php');
require_once(APPPATH . 'repositories/FootprintRepository.php');

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Dashboard extends MY_Controller {
	
	 /**
     * Constructor de la clase Dashboard.
     * Carga los helpers necesarios y realiza configuraciones iniciales.
     */

    protected $footprintService;
    public function __construct()
    {
        parent::__construct();
        $this->load->helper(['currency']);
		$this->load->model('Users_model');
        $footprintRepository = new FootprintRepository();
        $this->footprintService = new FootprintService($footprintRepository);
        // $this->access_only_allowed_members(); // Descomentarlo si es necesario

        // Bloqueo de URL para clientes (comentado por ahora)
        /*
        if ($this->login_user->user_type === "client") {
            $this->block_url_client_context($this->login_user->client_id, 14);
        }
        */

        
    }

    /**
     * Método principal que maneja la vista del dashboard.
     */
    public function index(): void
    {
        if ($this->login_user->user_type === "staff") {
            $view_data = $this->prepare_staff_dashboard();
            $this->template->rander("dashboard/index", $view_data);
        } else {
            $this->redirect_client_dashboard();
        }
    }

    /**
     * Guarda una nota adhesiva para el usuario.
     */
    public function save_sticky_note(): void
    {
        $sticky_note = $this->input->post("sticky_note", TRUE);
        $note_data = ["sticky_note" => $sticky_note];
        $this->Users_model->save($note_data, $this->login_user->id);
    }

    /**
     * Muestra la vista del dashboard para un proyecto específico.
     *
     * @param int $project_id ID del proyecto.
     */
    function view($id_proyecto = 0){
        // Cargar el driver de cache con la configuración
        $this->load->driver('cache', [
            'adapter' => $this->config->item('cache_driver'),  // 'redis'
            'backup'  => $this->config->item('cache_backup'),   // 'file'
            'redis'   => $this->config->item('redis')          // Array con host, password, port, timeout
        ]);        // generar clave de cache
        $cache_key = 'dashboard_view_'.$id_proyecto;
        //obtener el contenido cacheado
        if($cache_output = $this->cache->get($cache_key)){
            echo $cache_output;
            return;
        }
        ini_set('memory_limit', '4096M');
		
		$this->member_allowed($id_proyecto);
	
		if($id_proyecto){
			$this->session->set_userdata('project_context', $id_proyecto);
		}
		
		$id_cliente = $this->login_user->client_id;
		$project_info = $this->Projects_model->get_details(array("id" => $id_proyecto))->row();
		$rubro = $this->Industries_model->get_one($project_info->id_industria);
		$subrubro = $this->Subindustries_model->get_one($project_info->id_tecnologia);

		// Huella ACV
		$footprints = $this->Footprints_model->get_footprints_of_methodology(1)->result(); // Metodología con id 1: ReCiPe 2008, midpoint (H) [v1.11, December 2014
		$footprint_ids = array();
		foreach($footprints as $footprint){
			$footprint_ids[] = $footprint->id;
		}
		$options_footprint_ids = array("footprint_ids" => $footprint_ids);
		$huellas = $this->Project_rel_footprints_model->get_footprints_of_project($id_proyecto, $options_footprint_ids)->result();


		// Huella de Carbono
		$footprints_carbon = $this->Footprints_model->get_footprints_of_methodology(2)->result(); // Metodología con id 2: GHG Protocol
		$footprint_ids_carbon = array();
		foreach($footprints_carbon as $footprint_carbon){
			$footprint_ids_carbon[] = $footprint_carbon->id;
		}
		$options_footprint_ids_carbon = array("footprint_ids" => $footprint_ids_carbon);
		$huellas_carbon = $this->Project_rel_footprints_model->get_footprints_of_project($id_proyecto, $options_footprint_ids_carbon)->result();

		// Huella de Agua
		$footprints_water = $this->Footprints_model->get_footprints_of_methodology(3)->result(); // Metodología con id 2: Huella de Agua
		$footprint_ids_water = array();
		foreach($footprints_water as $footprint_water){
			$footprint_ids_water[] = $footprint_water->id;
		}
		$options_footprint_ids_water = array("footprint_ids" => $footprint_ids_water);
		$huellas_water = $this->Project_rel_footprints_model->get_footprints_of_project($id_proyecto, $options_footprint_ids_water)->result();

		$unidades_funcionales = $this->Functional_units_model->get_details(array("id_cliente" => $id_cliente, "id_proyecto" => $id_proyecto))->result();
		
		$view_data['id_proyecto'] = $id_proyecto;
		$view_data['rubro'] = $rubro->nombre;
		$view_data['subrubro'] = $subrubro->nombre;
		
		$view_data['unidades_funcionales'] = $unidades_funcionales;
		$view_data['criterios_calculos'] = $this->Unit_processes_model->get_rules_calculations_of_project($id_cliente, $project_info->id)->result();
		$view_data['procesos_unitarios'] = $this->Unit_processes_model->get_pu_of_projects($project_info->id)->result_array();
		$view_data['proyecto'] = $project_info;

		$view_data['huellas'] = $huellas;
		$view_data['huellas_carbon'] = $huellas_carbon;
		$view_data['huellas_water'] = $huellas_water;

		$view_data['client_id'] = $id_cliente;
		
		$view_data['Calculation_model'] = $this->Calculation_model;
		$view_data['Fields_model'] = $this->Fields_model;
		$view_data['Unity_model'] = $this->Unity_model;
		$view_data["Forms_model"] = $this->Forms_model;
		$view_data['Characterization_factors_model'] = $this->Characterization_factors_model;
		$view_data['Form_rel_materiales_rel_categorias_model'] = $this->Form_rel_materiales_rel_categorias_model;
		//$view_data['Unit_processes_model'] = $this->Unit_processes_model;
		//$view_data['Assignment_model'] = $this->Assignment_model;
		$view_data['Assignment_combinations_model'] = $this->Assignment_combinations_model;
		$view_data['Module_footprint_units_model'] = $this->Module_footprint_units_model;
		$view_data['Conversion_model'] = $this->Conversion_model;
		$view_data['Tipo_tratamiento_model'] = $this->Tipo_tratamiento_model;
		
		$view_data['id_unidad_volumen'] = $this->Reports_units_settings_model->get_one_where(array("id_cliente" => $id_cliente, "id_proyecto" => $id_proyecto, "id_tipo_unidad" => 2, "deleted" => 0))->id_unidad;
		$view_data['id_unidad_masa'] = $this->Reports_units_settings_model->get_one_where(array("id_cliente" => $id_cliente, "id_proyecto" => $id_proyecto, "id_tipo_unidad" => 1, "deleted" => 0))->id_unidad;
		$view_data['id_unidad_energia'] = $this->Reports_units_settings_model->get_one_where(array("id_cliente" => $id_cliente, "id_proyecto" => $id_proyecto, "id_tipo_unidad" => 4, "deleted" => 0))->id_unidad;
		
		$view_data['unidad_volumen'] = $this->Unity_model->get_one($view_data['id_unidad_volumen'])->nombre;
		$view_data['unidad_masa'] = $this->Unity_model->get_one($view_data['id_unidad_masa'])->nombre;
		$view_data['unidad_energia'] = $this->Unity_model->get_one($view_data['id_unidad_energia'])->nombre;
		
		$view_data['unidad_volumen_nombre_real'] = $this->Unity_model->get_one($view_data['id_unidad_volumen'])->nombre_real;
		$view_data['unidad_masa_nombre_real'] = $this->Unity_model->get_one($view_data['id_unidad_masa'])->nombre_real;
		$view_data['unidad_energia_nombre_real'] = $this->Unity_model->get_one($view_data['id_unidad_energia'])->nombre_real;
		
		//$view_data['campos_unidad_consumo'] = $this->Fields_model->get_unity_fields_of_ra($client_info->id, $id_proyecto, "Consumo")->result();
		//$view_data['campos_unidad_residuo'] = $this->Fields_model->get_unity_fields_of_ra($client_info->id, $id_proyecto, "Residuo")->result();
		
		$view_data['campos_unidad_consumo'] = $this->Forms_model->get_details(array("id_cliente" => $id_cliente, "id_proyecto" => $id_proyecto, "flujo" => "Consumo"))->result();
		$view_data['campos_unidad_residuo'] = $this->Forms_model->get_details(array("id_cliente" => $id_cliente, "id_proyecto" => $id_proyecto, "flujo" => "Residuo"))->result();
		
		$view_data['environmental_footprints_settings'] = $this->Client_environmental_footprints_settings_model->get_all_where(array("id_cliente" => $id_cliente, "id_proyecto" => $id_proyecto, "deleted" => 0))->result();
		$view_data['Client_consumptions_settings_model'] = $this->Client_consumptions_settings_model;
		$view_data['Client_waste_settings_model'] = $this->Client_waste_settings_model;
		
		$view_data['general_settings'] = $this->General_settings_model->get_one_where(array("id_proyecto" => $id_proyecto, "deleted" => 0));
		
		$view_data["Categories_alias_model"] = $this->Categories_alias_model;
		$view_data["Categories_model"] = $this->Categories_model;

		// PARA MOSTRAR LOS RESULTADOS DE HUELLAS SOLO DEL AÑO EN CURSO
		$view_data["first_date_current_year"] = date('2023-06-01');
		$view_data["last_date_current_year"] = date('Y-12-31');
		
	

		// ARREGLO DE LOS AÑOS QUE SE MOSTRARÁN EN LOS GRÁFICOS
		$current_year = date('Y');

		//AÑO ACTUAL + LOS ULTIMOS 2 AÑOS
		$years = range($current_year - 2, $current_year);
		
		$view_data['years'] = $years;
		
		// ARREGLO DE LOS MESES QUE SE MOSTRARÁN EN LOS GRÁFICOS AL HACER CLICK EN UNA COLUMNA
		$meses = array("Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre");
		$view_data['meses'] = $meses;

		// CONSUMO
		// DATOS PARA GRÁFICO Y TABLA CONSUMOS VOLUMEN
		$formularios_flujo_consumo = $view_data['campos_unidad_consumo'];
		$id_unidad_volumen_configuracion = $view_data['id_unidad_volumen'];
		$array_id_materiales_valores_volumen = $this->calculo_valores_por_flujo_material($formularios_flujo_consumo, 2, 'Consumo', $id_unidad_volumen_configuracion, $years, $meses); // tipo_unidad 2 es Volumen
		
		$view_data['array_id_materiales_valores_volumen'] = $array_id_materiales_valores_volumen;

		$array_grafico_consumos_volumen_data = $this->generar_datos_grafico($array_id_materiales_valores_volumen, 'Consumo', $id_cliente, $id_proyecto, $years, $meses);
		
		$view_data['array_grafico_consumos_volumen_data'] = $array_grafico_consumos_volumen_data;
		// FIN DATOS PARA GRÁFICO Y TABLA CONSUMOS VOLUMEN

		// DATOS PARA GRÁFICO Y TABLA CONSUMOS MASA
		$id_unidad_masa_configuracion = $view_data['id_unidad_masa'];
		$array_id_materiales_valores_masa = $this->calculo_valores_por_flujo_material($formularios_flujo_consumo, 1, 'Consumo', $id_unidad_masa_configuracion, $years, $meses); // tipo_unidad 1 es Masa
		
		$view_data['array_id_materiales_valores_masa'] = $array_id_materiales_valores_masa;
		// echo '<pre>'; var_dump($array_id_materiales_valores_masa); exit;

		$array_grafico_consumos_masa_data = $this->generar_datos_grafico($array_id_materiales_valores_masa, 'Consumo', $id_cliente, $id_proyecto, $years, $meses);
		
		// echo '<pre>'; var_dump($array_grafico_consumos_masa_data); exit;
		$view_data['array_grafico_consumos_masa_data'] = $array_grafico_consumos_masa_data;
		
		// if($this->login_user->id == 5){
		// 	echo "<pre>";
		// 	var_dump($view_data['array_grafico_consumos_masa_data']);
		// 	echo "</pre>";
		// 	exit();
		// }
		

		// FIN DATOS PARA GRÁFICO Y TABLA CONSUMOS MASA

		// DATOS PARA GRÁFICO Y TABLA CONSUMOS ENERGÍA
		$id_unidad_energia_configuracion = $view_data['id_unidad_energia'];
		$array_id_materiales_valores_energia = $this->calculo_valores_por_flujo_material($formularios_flujo_consumo, 4, 'Consumo', $id_unidad_energia_configuracion, $years, $meses); // tipo_unidad 4 es Energia
		
		$view_data['array_id_materiales_valores_energia'] = $array_id_materiales_valores_energia;
		// echo '<pre>'; var_dump($array_id_materiales_valores_energia); exit;

		$array_grafico_consumos_energia_data = $this->generar_datos_grafico($array_id_materiales_valores_energia, 'Consumo', $id_cliente, $id_proyecto, $years, $meses);
		
		// echo '<pre>'; var_dump($array_grafico_consumos_energia_data); exit;
		$view_data['array_grafico_consumos_energia_data'] = $array_grafico_consumos_energia_data;
		// FIN DATOS PARA GRÁFICO Y TABLA CONSUMOS ENERGÍA
		// FIN CONSUMO

		
		// RESIDUO
		// DATOS PARA GRÁFICO Y TABLA RESIDUOS VOLUMEN
		$formularios_flujo_residuo = $view_data['campos_unidad_residuo'];
		
		$id_unidad_volumen_configuracion = $view_data['id_unidad_volumen'];
		$array_id_materiales_valores_volumen_residuo = $this->calculo_valores_por_flujo_material($formularios_flujo_residuo, 2, 'Residuo', $id_unidad_volumen_configuracion, $years, $meses); // tipo_unidad 2 es Volumen
		
		// echo '<pre>'; var_dump($array_id_materiales_valores_volumen_residuo); exit;
		$view_data['array_id_materiales_valores_volumen_residuo'] = $array_id_materiales_valores_volumen_residuo;
		
		$array_grafico_residuos_volumen_data = $this->generar_datos_grafico($array_id_materiales_valores_volumen_residuo, 'Residuo', $id_cliente, $id_proyecto, $years, $meses);
		
		// echo '<pre>'; var_dump($array_grafico_residuos_volumen_data);exit;
		$view_data['array_grafico_residuos_volumen_data'] = $array_grafico_residuos_volumen_data;
		// FIN DATOS PARA GRÁFICO Y TABLA RESIDUOS VOLUMEN

		// DATOS PARA GRÁFICO Y TABLA RESIDUOS MASA
		$id_unidad_masa_configuracion = $view_data['id_unidad_masa'];
		$array_id_materiales_valores_masa_residuo = $this->calculo_valores_por_flujo_material($formularios_flujo_residuo, 1, 'Residuo', $id_unidad_masa_configuracion, $years, $meses); // tipo_unidad 1 es Masa
		
		$view_data['array_id_materiales_valores_masa_residuo'] = $array_id_materiales_valores_masa_residuo;
		// echo '<pre>'; var_dump($array_id_materiales_valores_masa_residuo); exit;

		$array_grafico_residuos_masa_data = $this->generar_datos_grafico($array_id_materiales_valores_masa_residuo, 'Residuo', $id_cliente, $id_proyecto, $years, $meses);
		
		// echo '<pre>'; var_dump($array_grafico_residuos_masa_data); exit;
		$view_data['array_grafico_residuos_masa_data'] = $array_grafico_residuos_masa_data;
		// FIN DATOS PARA GRÁFICO Y TABLA RESIDUOS MASA
		// FIN RESIDUO

		// if($this->login_user->id == 5){
		// 	exit();
		// }

		/* PARA CONFIGURACIÓN PANEL PRINCIPAL */

		$array_id_categorias_valores_consumo_masa = array();
		$array_id_categorias_valores_consumo_volumen = array();
		$array_id_categorias_valores_consumo_energia = array();
		foreach($view_data['campos_unidad_consumo'] as $formulario_campo){
			
			$datos_campo = json_decode($formulario_campo->unidad, true);
			$id_tipo_unidad = $datos_campo["tipo_unidad_id"];
			
			if($id_tipo_unidad == 1){ //MASA
				$id_formulario = $formulario_campo->id;
				$categorias = $this->Form_rel_materiales_rel_categorias_model->get_all_where(array("id_formulario" => $id_formulario))->result();
				foreach($categorias as $cat){
					$array_id_categorias_valores_consumo_masa[$cat->id_categoria] = $cat->id_categoria;
				}
			}
			
			if($id_tipo_unidad == 2){ //VOLUMEN
				$id_formulario = $formulario_campo->id;
				$categorias = $this->Form_rel_materiales_rel_categorias_model->get_all_where(array("id_formulario" => $id_formulario))->result();
				foreach($categorias as $cat){
					$array_id_categorias_valores_consumo_volumen[$cat->id_categoria] = $cat->id_categoria;
				}
			}
			
			if($id_tipo_unidad == 4){ //ENERGIA
				$id_formulario = $formulario_campo->id;
				$categorias = $this->Form_rel_materiales_rel_categorias_model->get_all_where(array("id_formulario" => $id_formulario))->result();
				foreach($categorias as $cat){
					$array_id_categorias_valores_consumo_energia[$cat->id_categoria] = $cat->id_categoria;
				}
			}
			
		}
		
		$array_id_categorias_valores_residuo_masa = array();
		$array_id_categorias_valores_residuo_volumen = array();
		foreach($view_data['campos_unidad_residuo'] as $formulario_campo){
			
			$datos_campo = json_decode($formulario_campo->unidad, true);
			$id_tipo_unidad = $datos_campo["tipo_unidad_id"];
			
			if($id_tipo_unidad == 1){ //MASA
				$id_formulario = $formulario_campo->id;
				$categorias = $this->Form_rel_materiales_rel_categorias_model->get_all_where(array("id_formulario" => $id_formulario))->result();
				foreach($categorias as $cat){
					$array_id_categorias_valores_residuo_masa[$cat->id_categoria] = $cat->id_categoria;
				}
			}
			
			if($id_tipo_unidad == 2){ //VOLUMEN
				$id_formulario = $formulario_campo->id;
				$categorias = $this->Form_rel_materiales_rel_categorias_model->get_all_where(array("id_formulario" => $id_formulario))->result();
				foreach($categorias as $cat){
					$array_id_categorias_valores_residuo_volumen[$cat->id_categoria] = $cat->id_categoria;
				}
			}
			
		}

		$client_consumption_settings = $this->Client_consumptions_settings_model->get_all_where(array(
			"id_cliente" => $this->login_user->client_id,
			"id_proyecto" => $id_proyecto,
			"deleted" => 0
		))->result_array();
		
		$ocultar_tabla_consumos_volumen = TRUE;	
		foreach($client_consumption_settings as $setting){
			if(in_array($setting["id_categoria"], $array_id_categorias_valores_consumo_volumen)){
				if($setting["tabla"]){
					$ocultar_tabla_consumos_volumen = FALSE;
					break;
				}
			}
		}
		
		$ocultar_grafico_consumos_volumen = TRUE;
		foreach($client_consumption_settings as $setting){
			if(in_array($setting["id_categoria"], $array_id_categorias_valores_consumo_volumen)){
				if($setting["grafico"]){
					$ocultar_grafico_consumos_volumen = FALSE;
					break;
				}
			}			
		}
			
		$ocultar_tabla_consumos_masa = TRUE;
		foreach($client_consumption_settings as $setting){
			if(in_array($setting["id_categoria"], $array_id_categorias_valores_consumo_masa)){
				if($setting["tabla"]){
					$ocultar_tabla_consumos_masa = FALSE;
					break;
				}
			}
		}
				
		$ocultar_grafico_consumos_masa = TRUE;
		foreach($client_consumption_settings as $setting){
			if(in_array($setting["id_categoria"], $array_id_categorias_valores_consumo_masa)){
				if($setting["grafico"]){
					$ocultar_grafico_consumos_masa = FALSE;
					break;
				}
			}			
		}
		
		$ocultar_tabla_consumos_energia = TRUE;
		foreach($client_consumption_settings as $setting){
			if(in_array($setting["id_categoria"], $array_id_categorias_valores_consumo_energia)){
				if($setting["tabla"]){
					$ocultar_tabla_consumos_energia = FALSE;
					break;
				}
			}
		}
				
		$ocultar_grafico_consumos_energia = TRUE;
		foreach($client_consumption_settings as $setting){
			if(in_array($setting["id_categoria"], $array_id_categorias_valores_consumo_energia)){
				if($setting["grafico"]){
					$ocultar_grafico_consumos_energia = FALSE;
					break;
				}
			}			
		}
		
		$client_waste_settings = $this->Client_waste_settings_model->get_all_where(array(
			"id_cliente" => $this->login_user->client_id,
			"id_proyecto" => $id_proyecto,
			"deleted" => 0
		))->result_array();
		
		$ocultar_tabla_residuos_volumen = TRUE;		
		foreach($client_waste_settings as $setting){
			if(in_array($setting["id_categoria"], $array_id_categorias_valores_residuo_volumen)){
				if($setting["tabla"]){
					$ocultar_tabla_residuos_volumen = FALSE;
					break;
				}
			}
		}

		$ocultar_grafico_residuos_volumen = TRUE;		
		foreach($client_waste_settings as $setting){
			if(in_array($setting["id_categoria"], $array_id_categorias_valores_residuo_volumen)){
				if($setting["grafico"]){
					$ocultar_grafico_residuos_volumen = FALSE;
					break;
				}
			}
		}
		
		
		$ocultar_tabla_residuos_masa = TRUE;		
		foreach($client_waste_settings as $setting){
			if(in_array($setting["id_categoria"], $array_id_categorias_valores_residuo_masa)){
				if($setting["tabla"]){
					$ocultar_tabla_residuos_masa = FALSE;
					break;
				}
			}
		}
		
		$ocultar_grafico_residuos_masa = TRUE;		
		foreach($client_waste_settings as $setting){
			if(in_array($setting["id_categoria"], $array_id_categorias_valores_residuo_masa)){
				if($setting["grafico"]){
					$ocultar_grafico_residuos_masa = FALSE;
					break;
				}
			}
		}
		
		$view_data["ocultar_tabla_consumos_volumen"] = $ocultar_tabla_consumos_volumen;
		$view_data["ocultar_grafico_consumos_volumen"] = $ocultar_grafico_consumos_volumen;
		$view_data["ocultar_tabla_consumos_masa"] = $ocultar_tabla_consumos_masa;
		$view_data["ocultar_grafico_consumos_masa"] = $ocultar_grafico_consumos_masa;
		$view_data["ocultar_tabla_consumos_energia"] = $ocultar_tabla_consumos_energia;
		$view_data["ocultar_grafico_consumos_energia"] = $ocultar_grafico_consumos_energia;
		
		$view_data["ocultar_tabla_residuos_volumen"] = $ocultar_tabla_residuos_volumen;
		$view_data["ocultar_grafico_residuos_volumen"] = $ocultar_grafico_residuos_volumen;
		$view_data["ocultar_tabla_residuos_masa"] = $ocultar_tabla_residuos_masa;
		$view_data["ocultar_grafico_residuos_masa"] = $ocultar_grafico_residuos_masa;
		
		// Compromises
		$id_compromiso_rca = $this->Compromises_rca_model->get_one_where(array('id_proyecto' => $id_proyecto, 'deleted' => 0))->id;
		if($id_compromiso_rca){
			
			// COMPROMISOS AMBIENTALES - RCA

			// EVALUADOS
			$evaluados = $this->Evaluated_rca_compromises_model->get_all_where(
				array(
					"id_compromiso" => $id_compromiso_rca, 
					"deleted" => 0
				)
			)->result();
			
			// ESTADOS RCA
			$estados_cliente = $this->Compromises_compliance_status_model->get_details(
				array(
					"id_cliente" => $id_cliente, 
					"tipo_evaluacion" => "rca",
				)
			)->result();
			
			// ULTIMAS EVALUACIONES
			$ultimas_evaluaciones = $this->Compromises_compliance_evaluation_rca_model->get_last_evaluations_of_project(
				$id_proyecto, 
				NULL
			)->result();
			
			// PROCESAR TABLA
			$array_estados = array();
			$total = 0;
			
			foreach($estados_cliente as $estado) {
				
				$id_estado = $estado->id;
				
				if($estado->categoria == "No Aplica"){
					continue;
				}
				$array_estados[$estado->id] = array(
					"nombre_estado" => $estado->nombre_estado,
					"categoria" => $estado->categoria,
					"color" => $estado->color,
					"evaluaciones" => array(),
					"cantidad_categoria" => 0,
				);
				
				$cant = 0;
				foreach($evaluados as $evaluado) {
					
					$id_evaluado = $evaluado->id;
					
					foreach($ultimas_evaluaciones as $ultima_evaluacion) {
						if(
							$ultima_evaluacion->id_estados_cumplimiento_compromiso == $id_estado && 
							$ultima_evaluacion->id_evaluado == $id_evaluado
						){
							$array_estados_evaluados[$id_estado]["evaluaciones"][] = $ultima_evaluacion;
							$cant++;
						}
					}
				}
				
				$array_estados[$id_estado]["cantidad_categoria"] = $cant;
				$total += $cant;
				
			}
			
			$view_data["total_compromisos_aplicables"] = $total;
			$view_data["total_cantidades_estados_evaluados"] = $array_estados;
			
			//Compromises settings
			$view_data["Client_compromises_settings_model"] = $this->Client_compromises_settings_model;
			//traer perfilamiento del módulo
			$view_data["puede_ver_compromisos"] = $this->profile_access($this->session->user_id, 6, "ver", 3);
			//traer disponibilidad del módulo
			$view_data["disponibilidad_modulo_compromisos"] = $this->Module_availability_model->get_one_where(array("id_cliente" => $this->login_user->client_id, "id_proyecto" => $id_proyecto, "id_modulo_cliente" => 6, "deleted" => 0))->available;
			
		}

		
		// Permittings
		$id_permiso = $this->Permitting_model->get_one_where(array('id_proyecto' => $id_proyecto, 'deleted' => 0))->id;	
		if($id_permiso){
			
			// EVALUADOS
			$evaluados = $this->Evaluated_permitting_model->get_all_where(
				array(
					"id_permiso" => $id_permiso, 
					"deleted" => 0
				)
			)->result();
			
			// ESTADOS
			$estados_cliente = $this->Permitting_procedure_status_model->get_details(
				array(
					"id_cliente" => $id_cliente,
				)
			)->result();
			
			// ULTIMAS EVALUACIONES
			$ultimas_evaluaciones = $this->Permitting_procedure_evaluation_model->get_last_evaluations_of_project(
				$id_proyecto, 
				NULL
			)->result();
			
			// PROCESAR TABLA
			$array_estados = array();
			$total = 0;
			
			foreach($estados_cliente as $estado) {
				
				$id_estado = $estado->id;
				
				if($estado->categoria == "No Aplica"){
					continue;
				}
				$array_estados[$estado->id] = array(
					"nombre_estado" => $estado->nombre_estado,
					"categoria" => $estado->categoria,
					"color" => $estado->color,
					"evaluaciones" => array(),
					"cantidad_categoria" => 0,
				);
				
				$cant = 0;
				foreach($evaluados as $evaluado) {
					
					$id_evaluado = $evaluado->id;
					
					foreach($ultimas_evaluaciones as $ultima_evaluacion) {
						if(
							$ultima_evaluacion->id_estados_tramitacion_permisos == $id_estado && 
							$ultima_evaluacion->id_evaluado == $id_evaluado
						){
							$array_estados[$id_estado]["evaluaciones"][] = $ultima_evaluacion;
							$cant++;
						}
					}
				}
				
				$array_estados[$id_estado]["cantidad_categoria"] = $cant;
				$total += $cant;
			}
			
			$view_data["total_permisos_aplicables"] = $total;
			$view_data["total_cantidades_estados_evaluados_permisos"] = $array_estados;

			//Permitting settings 
			$view_data["Client_permitting_settings_model"] = $this->Client_permitting_settings_model;
			//traer perfilamiento del módulo
			$view_data["puede_ver_permisos"] = $this->profile_access($this->session->user_id, 7, "ver", 5);
			//traer disponibilidad del módulo
			$view_data["disponibilidad_modulo_permisos"] = $this->Module_availability_model->get_one_where(array("id_cliente" => $this->login_user->client_id, "id_proyecto" => $id_proyecto, "id_modulo_cliente" => 7, "deleted" => 0))->available;
		}

		/* UNIDADES FUNCIONALES - CÁLCULO 2.0*/
		$array_factores = array();
		$factores = $this->Calculation_model->get_factores($id_proyecto)->result();
		foreach($factores as $factor) {
			$array_factores[$factor->id_bd][$factor->id_metodologia][$factor->id_huella][$factor->id_material][$factor->id_categoria][$factor->id_subcategoria][$factor->id_unidad] = (float)$factor->factor;
		}

		$array_transformaciones = array();
		$transformaciones = $this->Calculation_model->get_transformaciones($id_proyecto)->result();
		foreach($transformaciones as $transformacion) {
			$array_transformaciones[$transformacion->id] = (float)$transformacion->transformacion;
		}

		//$view_data["huellas"] = $this->Project_rel_footprints_model->get_dropdown_list(array("id_huella"), "id_huella", array("id_proyecto" => 1));
		$view_data["huellas"] = $huellas;
		$view_data["huellas_carbon"] = $huellas_carbon;
		$view_data["huellas_water"] = $huellas_water;
		$view_data["sp_uf"] = $this->Functional_units_model->get_dropdown_list(array("id"), "id_subproyecto", array("id_proyecto" => $id_proyecto));
		$view_data["campos_unidad"] = $this->Fields_model->get_dropdown_list(array("opciones"), "id", array("id_proyecto" => $id_proyecto, "id_tipo_campo" => 15));
		$view_data["unidades"] = $this->Unity_model->get_dropdown_list(array("nombre"), 'id');
		$view_data["tipo_tratamiento"] = $this->Tipo_tratamiento_model->get_dropdown_list(array("nombre"), "id", array("deleted" => 0));
		$view_data["type_of_origin_matter"] = $this->EC_Types_of_origin_matter_model->get_dropdown_list(array("nombre"), 'id');
		$view_data["type_of_origin"] = $this->EC_Types_of_origin_model->get_dropdown_list(array("nombre"), 'id');
		$view_data["default_type"] = $this->EC_Types_no_apply_model->get_dropdown_list(array("nombre"), 'id');
		$view_data["array_factores"] = $array_factores;
		$view_data["array_transformaciones"] = $array_transformaciones;
		$view_data["calculos"] = $this->Calculation_model->get_calculos($id_proyecto, $id_cliente, NULL, NULL, NULL)->result();
		$view_data["sucursales"] = $this->Subprojects_model->get_dropdown_list(array("nombre"), "id", array("id_proyecto" => $id_proyecto, "deleted" => 0));
		
        $this->output->enable_profiler(false);
		$output = $this->template->rander("dashboard/client_dashboard", $view_data);
        $this->cache->save($cache_key, $output, 3600);
        //tirar la salida del cache
        echo $output;

    }

    /**
     * Verifica si el usuario es miembro permitido del proyecto.
     *
     * @param int $project_id ID del proyecto.
     */
    private function member_allowed(int $project_id): void
    {
        $user_id = $this->login_user->id;
        $is_member = $this->Project_members_model->get_all_where([
            "user_id" => $user_id,
            "project_id" => $project_id,
            "deleted" => 0
        ])->result();

			if (count($is_member) === 0) {
				redirect("forbidden");
			}
    }

    /**
     * Prepara los datos del dashboard para usuarios de tipo staff.
     *
     * @return array Datos para la vista.
     */
    private function prepare_staff_dashboard(): array
    {
        $settings = [
            'timeline' => get_setting("module_timeline"),
            'attendance' => get_setting("module_attendance"),
            'event' => get_setting("module_event"),
            'invoice' => get_setting("module_invoice"),
            'expense' => get_setting("module_expense"),
            'ticket' => get_setting("module_ticket"),
            'project_timesheet' => get_setting("module_project_timesheet"),
        ];

        $access = [
            'expense' => $this->get_access_info("expense"),
            'invoice' => $this->get_access_info("invoice"),
            'ticket' => $this->get_access_info("ticket"),
            'timecards' => $this->get_access_info("attendance"),
        ];

        $view_data = [
            'show_timeline' => (bool)$settings['timeline'],
            'show_attendance' => (bool)$settings['attendance'],
            'show_event' => (bool)$settings['event'],
            'show_project_timesheet' => (bool)$settings['project_timesheet'],
            'show_invoice_statistics' => false,
            'show_ticket_status' => false,
            'show_income_vs_expenses' => false,
            'show_clock_status' => false,
        ];

        // Determinar la visibilidad de los widgets basados en configuraciones y permisos
        if ($settings['invoice'] && $settings['expense'] && $access['expense']->access_type === "all" && $access['invoice']->access_type === "all") {
            $view_data["show_income_vs_expenses"] = true;
        }

        if ($settings['invoice'] && $access['invoice']->access_type === "all") {
            $view_data["show_invoice_statistics"] = true;
        }

        if ($settings['ticket'] && $access['ticket']->access_type === "all") {
            $view_data["show_ticket_status"] = true;
        }

        if ($settings['attendance'] && $access['timecards']->access_type === "all") {
            $view_data["show_clock_status"] = true;
        }

        return $view_data;
    }

    /**
     * Redirige a la vista correspondiente del dashboard del cliente.
     */
    private function redirect_client_dashboard(): void
    {
        if ($this->session->project_context) {
            redirect('home');
        } else {
            redirect('inicio_projects');
        }
    }

    /**
     * Obtiene la configuración de la unidad de reporte.
     *
     * @param int $client_id ID del cliente.
     * @param int $project_id ID del proyecto.
     * @param int $type_id Tipo de unidad (1: Masa, 2: Volumen, 4: Energía).
     * @return int ID de la unidad.
     */
    private function get_report_unit(int $client_id, int $project_id, int $type_id): int
    {
        $setting = $this->Reports_units_settings_model->get_one_where([
            "id_cliente" => $client_id,
            "id_proyecto" => $project_id,
            "id_tipo_unidad" => $type_id,
            "deleted" => 0
        ]);

        return $setting->id_unidad ?? 0;
    }

    /**
     * Obtiene las huellas de un proyecto basado en la metodología.
     *
     * @param int $project_id ID del proyecto.
     * @param int $methodology_id ID de la metodología (default: 1).
     * @return array Resultado de las huellas.
     */
    private function get_project_footprints(int $project_id, int $methodology_id = 1): array
    {
        $footprints = $this->Footprints_model->get_footprints_of_methodology($methodology_id)->result();
        $footprint_ids = array_column($footprints, 'id');

        return $this->Project_rel_footprints_model->get_footprints_of_project($project_id, ['footprint_ids' => $footprint_ids])->result();
    }

    /**
     * Prepara los factores para el cálculo.
     *
     * @param int $project_id ID del proyecto.
     * @return array Factores organizados.
     */
    private function prepare_factores(int $project_id): array
    {
        $factores = $this->Calculation_model->get_factores($project_id)->result();
        $array_factores = [];

        foreach ($factores as $factor) {
            $array_factores[$factor->id_bd][$factor->id_metodologia][$factor->id_huella][$factor->id_material][$factor->id_categoria][$factor->id_subcategoria][$factor->id_unidad] = (float)$factor->factor;
        }

        return $array_factores;
    }

    /**
     * Prepara las transformaciones para el cálculo.
     *
     * @param int $project_id ID del proyecto.
     * @return array Transformaciones organizadas.
     */
    private function prepare_transformaciones(int $project_id): array
    {
        $transformaciones = $this->Calculation_model->get_transformaciones($project_id)->result();
        $array_transformaciones = [];

        foreach ($transformaciones as $transformacion) {
            $array_transformaciones[$transformacion->id] = (float)$transformacion->transformacion;
        }

        return $array_transformaciones;
    }

    /**
     * Prepara los datos para los gráficos de consumo.
     *
     * @param array $view_data Datos de vista existentes.
     * @return array Datos adicionales para la vista.
     */
    private function prepare_consumption_charts(array $view_data): array
    {
        $years = $view_data['years'];
        $meses = $view_data['meses'];
        $client_id = $view_data['client_id'];
        $project_id = $view_data['id_proyecto'];
        $campos_consumo = $view_data['campos_unidad_consumo'];
        $id_unidad_volumen = $view_data['id_unidad_volumen'];
        $id_unidad_masa = $view_data['id_unidad_masa'];
        $id_unidad_energia = $view_data['id_unidad_energia'];

        // Calcular valores por flujo y material
        $valores_volumen = $this->calculo_valores_por_flujo_material($campos_consumo, 2, 'Consumo', $id_unidad_volumen, $years, $meses);
        $valores_masa = $this->calculo_valores_por_flujo_material($campos_consumo, 1, 'Consumo', $id_unidad_masa, $years, $meses);
        $valores_energia = $this->calculo_valores_por_flujo_material($campos_consumo, 4, 'Consumo', $id_unidad_energia, $years, $meses);

        // Generar datos para gráficos
        $grafico_volumen = $this->generar_datos_grafico($valores_volumen, 'Consumo', $client_id, $project_id, $years, $meses);
        $grafico_masa = $this->generar_datos_grafico($valores_masa, 'Consumo', $client_id, $project_id, $years, $meses);
        $grafico_energia = $this->generar_datos_grafico($valores_energia, 'Consumo', $client_id, $project_id, $years, $meses);

        return [
            'array_id_materiales_valores_volumen' => $valores_volumen,
            'array_grafico_consumos_volumen_data' => $grafico_volumen,
            'array_id_materiales_valores_masa' => $valores_masa,
            'array_grafico_consumos_masa_data' => $grafico_masa,
            'array_id_materiales_valores_energia' => $valores_energia,
            'array_grafico_consumos_energia_data' => $grafico_energia,
        ];
    }


    /**
     * Procesa los compromisos ambientales y prepara los datos para la vista.
     *
     * @param int $project_id ID del proyecto.
     * @param int $client_id ID del cliente.
     * @param array &$view_data Referencia a los datos de la vista.
     */
    private function process_compromises(int $project_id, int $client_id, array &$view_data): void
    {
        $compromiso_rca = $this->Compromises_rca_model->get_one_where([
            'id_proyecto' => $project_id,
            'deleted' => 0
        ]);

        if ($compromiso_rca && isset($compromiso_rca->id)) {
            $id_compromiso_rca = $compromiso_rca->id;

            // Obtener evaluados, estados y últimas evaluaciones
            $evaluados = $this->Evaluated_rca_compromises_model->get_all_where([
                "id_compromiso" => $id_compromiso_rca,
                "deleted" => 0
            ])->result();

            $estados_cliente = $this->Compromises_compliance_status_model->get_details([
                "id_cliente" => $client_id,
                "tipo_evaluacion" => "rca",
            ])->result();

            $ultimas_evaluaciones = $this->Compromises_compliance_evaluation_rca_model->get_last_evaluations_of_project($project_id, NULL)->result();

            // Procesar tabla de estados
            $array_estados = [];
            $total = 0;

            foreach ($estados_cliente as $estado) {
                if ($estado->categoria === "No Aplica") {
                    continue;
                }

                $array_estados[$estado->id] = [
                    "nombre_estado" => $estado->nombre_estado,
                    "categoria" => $estado->categoria,
                    "color" => $estado->color,
                    "evaluaciones" => [],
                    "cantidad_categoria" => 0,
                ];

                $cant = 0;
                foreach ($evaluados as $evaluado) {
                    foreach ($ultimas_evaluaciones as $ultima_evaluacion) {
                        if (
                            $ultima_evaluacion->id_estados_cumplimiento_compromiso === $estado->id &&
                            $ultima_evaluacion->id_evaluado === $evaluado->id
                        ) {
                            $array_estados[$estado->id]["evaluaciones"][] = $ultima_evaluacion;
                            $cant++;
                        }
                    }
                }

                $array_estados[$estado->id]["cantidad_categoria"] = $cant;
                $total += $cant;
            }

            $view_data["total_compromisos_aplicables"] = $total;
            $view_data["total_cantidades_estados_evaluados"] = $array_estados;

            // Configuraciones adicionales
            $view_data["Client_compromises_settings_model"] = $this->Client_compromises_settings_model;
            $view_data["puede_ver_compromisos"] = $this->profile_access($this->session->user_id, 6, "ver", 3);
            $view_data["disponibilidad_modulo_compromisos"] = $this->Module_availability_model->get_one_where([
                "id_cliente" => $client_id,
                "id_proyecto" => $project_id,
                "id_modulo_cliente" => 6,
                "deleted" => 0
            ])->available;
        }
    }

    /**
     * Procesa los permisos y prepara los datos para la vista.
     *
     * @param int $project_id ID del proyecto.
     * @param int $client_id ID del cliente.
     * @param array &$view_data Referencia a los datos de la vista.
     */
    private function process_permitting(int $project_id, int $client_id, array &$view_data): void
    {
        $permiso = $this->Permitting_model->get_one_where([
            'id_proyecto' => $project_id,
            'deleted' => 0
        ]);

        if ($permiso && isset($permiso->id)) {
            $id_permiso = $permiso->id;

            // Obtener evaluados, estados y últimas evaluaciones
            $evaluados = $this->Evaluated_permitting_model->get_all_where([
                "id_permiso" => $id_permiso,
                "deleted" => 0
            ])->result();

            $estados_cliente = $this->Permitting_procedure_status_model->get_details([
                "id_cliente" => $client_id,
            ])->result();

            $ultimas_evaluaciones = $this->Permitting_procedure_evaluation_model->get_last_evaluations_of_project($project_id, NULL)->result();

            // Procesar tabla de estados
            $array_estados = [];
            $total = 0;

            foreach ($estados_cliente as $estado) {
                if ($estado->categoria === "No Aplica") {
                    continue;
                }

                $array_estados[$estado->id] = [
                    "nombre_estado" => $estado->nombre_estado,
                    "categoria" => $estado->categoria,
                    "color" => $estado->color,
                    "evaluaciones" => [],
                    "cantidad_categoria" => 0,
                ];

                $cant = 0;
                foreach ($evaluados as $evaluado) {
                    foreach ($ultimas_evaluaciones as $ultima_evaluacion) {
                        if (
                            $ultima_evaluacion->id_estados_tramitacion_permisos === $estado->id &&
                            $ultima_evaluacion->id_evaluado === $evaluado->id
                        ) {
                            $array_estados[$estado->id]["evaluaciones"][] = $ultima_evaluacion;
                            $cant++;
                        }
                    }
                }

                $array_estados[$estado->id]["cantidad_categoria"] = $cant;
                $total += $cant;
            }

            $view_data["total_permisos_aplicables"] = $total;
            $view_data["total_cantidades_estados_evaluados_permisos"] = $array_estados;

            // Configuraciones adicionales
            $view_data["Client_permitting_settings_model"] = $this->Client_permitting_settings_model;
            $view_data["puede_ver_permisos"] = $this->profile_access($this->session->user_id, 7, "ver", 5);
            $view_data["disponibilidad_modulo_permisos"] = $this->Module_availability_model->get_one_where([
                "id_cliente" => $client_id,
                "id_proyecto" => $project_id,
                "id_modulo_cliente" => 7,
                "deleted" => 0
            ])->available;
        }
    }


    /**
     * Prepara los datos de drilldown para una categoría específica.
     *
     * @param array $valores Valores por flujo y material.
     * @param int $id_material ID del material.
     * @param int $year Año específico.
     * @param string $flujo Flujo ('Consumo' o 'Residuo').
     * @param int $client_id ID del cliente.
     * @param int $project_id ID del proyecto.
     * @return array Datos de drilldown para la categoría.
     */
    private function prepare_drilldown_material(
        array $valores,
        int $id_material,
        int $year,
        string $flujo,
        int $client_id,
        int $project_id
    ): array {
        $categorias = $valores[$id_material] ?? [];
        $serie = [
            'id' => "id_drilldown_material_{$id_material}_{$year}",
            'name' => "{$this->Materials_model->get_one($id_material)->nombre} {$year}",
            'data' => []
        ];

        foreach ($categorias as $id_categoria => $datos) {
            // Verificar si la categoría debe mostrarse en el gráfico
            $row_categoria = $flujo === 'Consumo'
                ? $this->Client_consumptions_settings_model->get_one_where([
                    'id_cliente' => $client_id,
                    'id_proyecto' => $project_id,
                    'id_categoria' => $id_categoria,
                    'deleted' => 0
                ])
                : $this->Client_waste_settings_model->get_one_where([
                    'id_cliente' => $client_id,
                    'id_proyecto' => $project_id,
                    'id_categoria' => $id_categoria,
                    'deleted' => 0
                ]);

            if ($row_categoria && $row_categoria->grafico) {
                $nombre_categoria = $this->get_categoria_nombre($id_categoria);
                $valor_categoria = array_sum($datos[$year] ?? []);

                if ($valor_categoria > 0) {
                    $serie['data'][] = [
                        'name' => $nombre_categoria,
                        'y' => $valor_categoria,
                        'id_categoria' => $id_categoria, // Para identificar la categoría en el siguiente drilldown
                    ];
                }
            }
        }

        // Añadir el año para referencia en drilldown de categorías
        $serie['year'] = $year;

        return $serie;
    }

    /**
     * Prepara los datos de drilldown para una categoría específica por mes.
     *
     * @param array $valores Valores por flujo y material.
     * @param int $id_categoria ID de la categoría.
     * @param int $year Año específico.
     * @param array $meses Meses considerados.
     * @return array Datos de drilldown para los meses.
     */
    private function prepare_drilldown_categoria(
        array $valores,
        int $id_categoria,
        int $year,
        array $meses
    ): array {
        $serie = [
            'id' => "id_drilldown_categoria_{$id_categoria}_{$year}",
            'name' => "{$this->get_categoria_nombre($id_categoria)} {$year}",
            'data' => []
        ];

        // Obtener todos los materiales y sumar los valores de la categoría
        foreach ($valores as $id_material => $categorias) {
            if (isset($categorias[$id_categoria])) {
                foreach ($categorias[$id_categoria][$year] ?? [] as $mes_num => $valor_mes) {
                    $serie['data'][] = [
                        'name' => $meses[$mes_num],
                        'y' => $valor_mes
                    ];
                }
            }
        }

        return $serie;
    }

    /**
     * Obtiene el nombre o alias de una categoría.
     *
     * @param int $id_categoria ID de la categoría.
     * @return string Nombre de la categoría.
     */
    private function get_categoria_nombre(int $id_categoria): string
    {
        $alias = $this->Categories_alias_model->get_one_where([
            'id_categoria' => $id_categoria,
            'id_cliente' => $this->login_user->client_id,
            'deleted' => 0
        ]);

        if ($alias && !empty($alias->alias)) {
            return $alias->alias;
        }

        $categoria = $this->Categories_model->get_one_where([
            'id' => $id_categoria,
            'deleted' => 0
        ]);

        return $categoria->nombre ?? 'Sin Nombre';
    }


    /**
     * Calcula los valores por flujo y material.
     *
     * @param array $formularios Formularios por flujo.
     * @param int $tipo_unidad Tipo de unidad (1: Masa, 2: Volumen, 4: Energía).
     * @param string $flujo Flujo ('Consumo' o 'Residuo').
     * @param int $unidad_configuracion ID de la unidad de configuración.
     * @param array $years Años a considerar.
     * @param array $meses Meses a considerar.
     * @return array Valores calculados.
     */
    private function calculo_valores_por_flujo_material(
        array $formularios,
        int $tipo_unidad,
        string $flujo,
        int $unidad_configuracion,
        array $years,
        array $meses
    ): array {
        $array_id_materiales_valores = [];

        foreach ($formularios as $formulario) {
            $datos_campo_unidad = json_decode($formulario->unidad, true);
            $id_tipo_unidad = $datos_campo_unidad["tipo_unidad_id"] ?? null;
            $id_unidad = $datos_campo_unidad["unidad_id"] ?? null;

            if ($id_tipo_unidad !== $tipo_unidad) {
                continue;
            }

            $id_formulario = $formulario->id;
            $materiales_rel_categorias = $this->Form_rel_materiales_rel_categorias_model->get_all_where([
                "id_formulario" => $id_formulario,
                "deleted" => 0
            ])->result();

            foreach ($materiales_rel_categorias as $mat_rel_cat) {
                // Inicializar valores
                foreach ($years as $year) {
                    foreach ($meses as $index => $mes) {
                        $array_id_materiales_valores[$mat_rel_cat->id_material][$mat_rel_cat->id_categoria][$year][$index] = 0;
                    }
                }

                // Obtener elementos del formulario
                $elementos_form = $this->Calculation_model->get_records_of_category_of_form(
                    $mat_rel_cat->id_categoria,
                    $mat_rel_cat->id_formulario,
                    $flujo
                )->result();

                foreach ($elementos_form as $elemento) {
                    $datos_decoded = json_decode($elemento->datos, true);
                    $fecha = $datos_decoded['fecha'] ?? null;
                    $valor = $datos_decoded["unidad_residuo"] ?? 0;

                    if (!$fecha) {
                        continue;
                    }

                    $fecha_timestamp = strtotime($fecha);
                    $agno = date('Y', $fecha_timestamp);
                    $mes_num = (int)date('n', $fecha_timestamp) - 1; // Índice de 0 a 11

                    if ($id_unidad === $unidad_configuracion) {
                        $array_id_materiales_valores[$mat_rel_cat->id_material][$mat_rel_cat->id_categoria][$agno][$mes_num] += $valor;
                    } else {
                        $conversion = $this->Conversion_model->get_one_where([
                            "id_tipo_unidad" => $tipo_unidad,
                            "id_unidad_origen" => $id_unidad,
                            "id_unidad_destino" => $unidad_configuracion
                        ]);

                        if ($conversion && isset($conversion->transformacion)) {
                            $valor_transformado = $valor * (float)$conversion->transformacion;
                            $array_id_materiales_valores[$mat_rel_cat->id_material][$mat_rel_cat->id_categoria][$agno][$mes_num] += $valor_transformado;
                        }
                    }
                }
            }
        }

        return $array_id_materiales_valores;
    }

    /**
     * Genera los datos para los gráficos basados en los valores calculados.
     *
     * @param array $valores Valores por flujo y material.
     * @param string $flujo Flujo ('Consumo' o 'Residuo').
     * @param int $client_id ID del cliente.
     * @param int $project_id ID del proyecto.
     * @param array $years Años considerados.
     * @param array $meses Meses considerados.
     * @return array Datos formateados para el gráfico.
     */
    private function generar_datos_grafico(
        array $valores,
        string $flujo,
        int $client_id,
        int $project_id,
        array $years,
        array $meses
    ): array {
        $series = [];
        $drilldown_series = [];

        foreach ($years as $year) {
            $serie = [
                'name' => (string)$year,
                'data' => [],
            ];

            foreach ($valores as $id_material => $categorias) {
                $total_material = 0;

                foreach ($categorias as $id_categoria => $datos) {
                    // Verificar si la categoría debe mostrarse en el gráfico
                    $row_categoria = $flujo === 'Consumo'
                        ? $this->Client_consumptions_settings_model->get_one_where([
                            'id_cliente' => $client_id,
                            'id_proyecto' => $project_id,
                            'id_categoria' => $id_categoria,
                            'deleted' => 0
                        ])
                        : $this->Client_waste_settings_model->get_one_where([
                            'id_cliente' => $client_id,
                            'id_proyecto' => $project_id,
                            'id_categoria' => $id_categoria,
                            'deleted' => 0
                        ]);

                    if ($row_categoria && $row_categoria->grafico) {
                        $valor_categoria = array_sum($datos[$year] ?? []);
                        $total_material += $valor_categoria;
                    }
                }

                if ($total_material > 0) {
                    $nombre_material = $this->Materials_model->get_one($id_material)->nombre;
                    $serie['data'][] = [
                        'name' => $nombre_material,
                        'y' => $total_material,
                        'drilldown' => "id_drilldown_material_{$id_material}_{$year}"
                    ];

                    // Preparar datos para drilldown (nivel 2: categorías)
                    $drilldown_series[] = $this->prepare_drilldown_material($valores, $id_material, $year, $flujo, $client_id, $project_id);
                }
            }

            $series[] = $serie;
        }

        // Procesar drilldown para categorías (nivel 2) y meses (nivel 3)
        $drilldown_complete = [];
        foreach ($drilldown_series as $drilldown) {
            $id_drilldown = $drilldown['id'];
            $data = $drilldown['data'];
            $year = $drilldown['year'];

            $serie = [
                'id' => $id_drilldown,
                'name' => $drilldown['name'],
                'data' => []
            ];

            foreach ($data as $categoria) {
                $id_categoria = $categoria['id_categoria'];
                $nombre_categoria = $this->get_categoria_nombre($id_categoria);
                $y = $categoria['y'];

                $serie['data'][] = [
                    'name' => $nombre_categoria,
                    'y' => $y,
                    'drilldown' => "id_drilldown_categoria_{$id_categoria}_{$year}"
                ];
            }

            $drilldown_complete[] = $serie;

            // Añadir drilldown para meses
            foreach ($data as $categoria) {
                $id_categoria = $categoria['id_categoria'];
                $drilldown_complete[] = $this->prepare_drilldown_categoria($valores, $id_categoria, $year, $meses);
            }
        }

        return [
            'series' => $series,
            'drilldown' => $drilldown_complete
        ];
    }

	/**
	 * Prepara los datos para los gráficos de residuo.
	 *
	 * @param array $view_data Datos de vista existentes.
	 * @return array Datos adicionales para la vista.
	 */
	private function prepare_waste_charts(array $view_data): array
	{
		$years = $view_data['years'];
		$meses = $view_data['meses'];
		$client_id = $view_data['client_id'];
		$project_id = $view_data['id_proyecto'];
		$campos_residuo = $view_data['campos_unidad_residuo'];
		$id_unidad_volumen = $view_data['id_unidad_volumen'];
		$id_unidad_masa = $view_data['id_unidad_masa'];

		// Calcular valores por flujo y material
		$valores_volumen_residuo = $this->calculo_valores_por_flujo_material($campos_residuo, 2, 'Residuo', $id_unidad_volumen, $years, $meses);
		$valores_masa_residuo = $this->calculo_valores_por_flujo_material($campos_residuo, 1, 'Residuo', $id_unidad_masa, $years, $meses);

		// Generar datos para gráficos
		$grafico_residuos_volumen = $this->generar_datos_grafico($valores_volumen_residuo, 'Residuo', $client_id, $project_id, $years, $meses);
		$grafico_residuos_masa = $this->generar_datos_grafico($valores_masa_residuo, 'Residuo', $client_id, $project_id, $years, $meses);

		return [
			'array_id_materiales_valores_volumen_residuo' => $valores_volumen_residuo,
			'array_grafico_residuos_volumen_data' => $grafico_residuos_volumen,
			'array_id_materiales_valores_masa_residuo' => $valores_masa_residuo,
			'array_grafico_residuos_masa_data' => $grafico_residuos_masa,
		];
	}






}


/* End of file dashboard.php */
/* Location: ./application/controllers/dashboard.php */